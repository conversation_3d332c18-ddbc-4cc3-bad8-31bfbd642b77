# 🔑 AI Podcast Generator - Demo Credentials

## 🚀 **Application Status: RUNNING**
**URL:** http://localhost:5000

---

## 👤 **User Accounts**

### 🛡️ **Administrator Account**
```
Username: admin
Password: admin123
Email:    <EMAIL>
Access:   Full admin privileges
```

**Features:**
- ✅ Complete admin dashboard
- ✅ User management system
- ✅ System statistics and analytics
- ✅ Create/manage demo users
- ✅ View all projects across users
- ✅ Full application access

**Admin Panel:** http://localhost:5000/admin

---

### 👥 **Demo User Accounts**

#### **Demo User 1**
```
Username: demo_user
Password: demo123
Email:    <EMAIL>
Access:   Standard user
```

#### **Demo User 2**
```
Username: content_creator
Password: creator123
Email:    <EMAIL>
Access:   Standard user
```

**Features:**
- ✅ Create podcast projects
- ✅ Generate AI content (with API keys)
- ✅ Download final videos
- ✅ Project management dashboard
- ✅ Real-time progress tracking

---

## 🎯 **Quick Access Options**

### **Option 1: One-Click Demo** ⚡
**URL:** http://localhost:5000/demo_login
- Instantly logs you in as `demo_user`
- No password required!
- Perfect for quick testing

### **Option 2: Manual Login** 🔐
**URL:** http://localhost:5000/login
- Use any credentials above
- Demo login button available
- Full authentication experience

### **Option 3: Admin Access** 👑
**URL:** http://localhost:5000/admin
- Login as admin first
- Access comprehensive admin panel
- Manage users and view system stats

---

## 🎨 **What You Can Test**

### **🎙️ User Experience**
1. **Create Projects:** Enter keywords, select options
2. **AI Workflow:** Watch real-time content generation
3. **Video Selection:** Choose from stock videos
4. **Download Results:** Get final podcast videos

### **📊 Admin Features**
1. **User Management:** View all registered users
2. **Project Overview:** Monitor all projects
3. **System Stats:** Usage analytics
4. **Demo Management:** Create additional accounts

### **🔧 Interface Features**
1. **Responsive Design:** Test on mobile/desktop
2. **Real-time Updates:** WebSocket progress tracking
3. **Project Dashboard:** Manage multiple projects
4. **Status Tracking:** Visual progress indicators

---

## 🔑 **API Keys (Optional)**

To enable full AI functionality, create `.env` file:

```env
# AI Services
OPENAI_API_KEY=your_openai_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
PEXELS_API_KEY=your_pexels_api_key_here

# Security
SECRET_KEY=your_secret_key_here
```

**Get API Keys:**
- **OpenAI:** https://platform.openai.com/api-keys
- **ElevenLabs:** https://elevenlabs.io/
- **Pexels:** https://www.pexels.com/api/

---

## 🎉 **Ready to Explore!**

### **🚀 Start Here:** http://localhost:5000

**Recommended Testing Flow:**
1. **Quick Demo:** Click "Try Demo" on homepage
2. **Create Project:** Test the full workflow
3. **Admin Panel:** Login as admin to see management features
4. **Multi-User:** Try different accounts

### **💡 Pro Tips:**
- Interface works perfectly without API keys
- Add API keys for full AI-powered experience
- All features are mobile-responsive
- Real-time updates work across all browsers

---

## 🔧 **Troubleshooting**

**Can't Access?**
- Ensure app is running: `python run.py`
- Check URL: http://localhost:5000

**Login Issues?**
- Try one-click demo: http://localhost:5000/demo_login
- Verify credentials are typed correctly

**Features Not Working?**
- Interface works without API keys
- Add API keys to `.env` for full functionality

---

**🎙️ Happy Testing! The AI Podcast Generator is ready for your exploration! ✨**
