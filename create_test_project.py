#!/usr/bin/env python3
"""
Create a test project that works without OpenAI API key
"""

import os
import sys

def create_working_test_project():
    """Create a test project with custom script (no OpenAI needed)"""
    print("🎯 Creating Test Project (No OpenAI API Key Required)...")
    
    try:
        from app import app, db, Project, User
        import uuid
        
        with app.app_context():
            # Get or create demo user
            demo_user = User.query.filter_by(username='demo').first()
            if not demo_user:
                # Create demo user
                demo_user = User(
                    username='demo',
                    email='<EMAIL>',
                    password_hash='demo_hash',
                    is_demo=True
                )
                db.session.add(demo_user)
                db.session.commit()
                print("✅ Created demo user")
            
            # Create test project with custom script
            test_script = """
            Speaker 1: Welcome to our technology podcast! Today we're discussing the latest innovations in artificial intelligence.
            
            Speaker 2: That's right! AI is transforming every industry. From healthcare to transportation, we're seeing incredible breakthroughs.
            
            Speaker 1: One area that's particularly exciting is machine learning. The ability for computers to learn and adapt without explicit programming is revolutionary.
            
            Speaker 2: Absolutely! And with the integration of AI in everyday applications, we're making technology more accessible and intuitive for everyone.
            
            Speaker 1: Thanks for joining us today. Don't forget to subscribe for more tech insights!
            """
            
            test_project = Project(
                id=str(uuid.uuid4()),
                user_id=demo_user.id,
                title="Test Project - Technology Podcast",
                content_type='paste',
                keyword='technology',  # This will be used for Pexels video search
                custom_script=test_script.strip(),
                script=test_script.strip(),
                language='en',
                num_speakers=2,
                tone='conversational',
                auto_create_video=True,
                status='created'
            )
            
            db.session.add(test_project)
            db.session.commit()
            
            print(f"✅ Test project created successfully!")
            print(f"📋 Project ID: {test_project.id}")
            print(f"🎬 Title: {test_project.title}")
            print(f"📝 Script length: {len(test_project.script)} characters")
            print(f"🔍 Keyword for videos: {test_project.keyword}")
            print(f"🌐 URL: http://localhost:5000/project/{test_project.id}")
            
            return test_project.id
            
    except Exception as e:
        print(f"❌ Error creating test project: {e}")
        return None

def test_pexels_search():
    """Test Pexels video search"""
    print("\n🎬 Testing Pexels Video Search...")
    
    try:
        from app import app
        from ai_services import VideoService
        
        with app.app_context():
            video_service = VideoService()
            
            # Search for technology videos
            videos = video_service.search_videos("technology", per_page=3)
            
            if videos:
                print(f"✅ Found {len(videos)} videos from Pexels!")
                for i, video in enumerate(videos):
                    print(f"  {i+1}. {video['width']}x{video['height']}, {video['duration']}s")
                return True
            else:
                print("❌ No videos found")
                return False
                
    except Exception as e:
        print(f"❌ Error testing Pexels: {e}")
        return False

def show_working_features():
    """Show what features are currently working"""
    print("\n" + "="*60)
    print("✅ WORKING FEATURES (No OpenAI API Key Required)")
    print("="*60)
    
    features = [
        "🎬 Pexels Video Integration:",
        "   - Real HD videos from Pexels",
        "   - Search by keyword",
        "   - Multiple video options",
        "",
        "📝 Custom Script Projects:",
        "   - Paste your own podcast script",
        "   - Multiple speakers support",
        "   - French language support",
        "",
        "🎨 Video Creation:",
        "   - Generated backgrounds (Abstract, Gradient, Minimal)",
        "   - Real Pexels videos",
        "   - Auto video creation option",
        "",
        "🗑️ Project Management:",
        "   - Delete projects with confirmation",
        "   - View project details",
        "   - Download final videos",
        "",
        "🌐 User Interface:",
        "   - Clean, professional design",
        "   - Responsive layout",
        "   - Real-time status updates"
    ]
    
    for feature in features:
        print(feature)

def show_next_steps():
    """Show what to do next"""
    print("\n🚀 NEXT STEPS:")
    print("1. 🌐 Visit: http://localhost:5000")
    print("2. 🔑 Click 'Demo Login'")
    print("3. 📋 Go to the test project URL shown above")
    print("4. 🎬 Click 'Create Video' to see Pexels videos!")
    print("5. 🎯 Create your own projects with custom scripts")
    print()
    print("💡 For AI generation features:")
    print("   - Get OpenAI API key from: https://platform.openai.com/account/api-keys")
    print("   - Run: python setup_openai_key.py")

if __name__ == "__main__":
    print("🎯 Creating Working Test Project")
    print("=" * 50)
    
    # Create test project
    project_id = create_working_test_project()
    
    if project_id:
        # Test Pexels integration
        pexels_working = test_pexels_search()
        
        # Show working features
        show_working_features()
        
        # Show next steps
        show_next_steps()
        
        print(f"\n🎉 SUCCESS! Test project ready at:")
        print(f"🌐 http://localhost:5000/project/{project_id}")
    else:
        print("\n❌ Failed to create test project")
        print("💡 Try running: python diagnose_error.py")
