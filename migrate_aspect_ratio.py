#!/usr/bin/env python3
"""
Database migration script to add aspect_ratio column to existing projects
"""

import os
import sys
from sqlalchemy import text

def migrate_database():
    """Add aspect_ratio column to existing projects"""
    try:
        from app import app, db
        
        with app.app_context():
            print("🔄 Starting database migration for aspect_ratio...")
            
            # Check if column already exists
            try:
                result = db.session.execute(text("SELECT aspect_ratio FROM project LIMIT 1"))
                print("✅ aspect_ratio column already exists")
                return True
            except Exception:
                print("📝 aspect_ratio column doesn't exist, adding it...")
            
            # Add the column with default value
            try:
                db.session.execute(text("ALTER TABLE project ADD COLUMN aspect_ratio VARCHAR(10) DEFAULT '16:9'"))
                db.session.commit()
                print("✅ Successfully added aspect_ratio column")
                
                # Update existing projects to have default aspect ratio
                result = db.session.execute(text("UPDATE project SET aspect_ratio = '16:9' WHERE aspect_ratio IS NULL"))
                db.session.commit()
                print(f"✅ Updated {result.rowcount} existing projects with default aspect ratio")
                
                return True
                
            except Exception as e:
                print(f"❌ Error adding column: {e}")
                db.session.rollback()
                return False
                
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def verify_migration():
    """Verify the migration was successful"""
    try:
        from app import app, db, Project
        
        with app.app_context():
            print("\n🔍 Verifying migration...")
            
            # Check if we can query the new column
            projects = Project.query.all()
            print(f"✅ Found {len(projects)} projects")
            
            # Check aspect ratios
            aspect_ratios = {}
            for project in projects:
                ratio = project.aspect_ratio or '16:9'
                aspect_ratios[ratio] = aspect_ratios.get(ratio, 0) + 1
            
            print("📊 Aspect ratio distribution:")
            for ratio, count in aspect_ratios.items():
                print(f"  {ratio}: {count} projects")
            
            return True
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def show_aspect_ratio_info():
    """Show information about aspect ratios"""
    print("\n" + "="*60)
    print("📐 ASPECT RATIO SUPPORT ADDED!")
    print("="*60)
    
    info = [
        "🎯 NEW FEATURES:",
        "",
        "✅ Aspect Ratio Selection:",
        "  📱 Portrait (9:16) - 1080x1920 - TikTok, Instagram Stories",
        "  🖥️ Landscape (16:9) - 1920x1080 - YouTube, TV",
        "  ⬜ Square (1:1) - 1080x1080 - Instagram Posts",
        "",
        "✅ Smart Background Generation:",
        "  - Backgrounds scale to match selected aspect ratio",
        "  - Text and elements adjust automatically",
        "  - Consistent quality across all formats",
        "",
        "✅ Video Processing:",
        "  - FFmpeg commands updated for different ratios",
        "  - Pexels videos work with all aspect ratios",
        "  - Captions positioned correctly for each format",
        "",
        "🎬 HOW TO USE:",
        "",
        "1. Create a new project at /create",
        "2. Select your desired video format:",
        "   - Portrait (9:16) for social media",
        "   - Landscape (16:9) for traditional video",
        "   - Square (1:1) for Instagram posts",
        "3. Generate your video as usual",
        "4. Download in your chosen format!",
        "",
        "💡 PERFECT FOR:",
        "  📱 TikTok, Instagram Stories, YouTube Shorts",
        "  🖥️ YouTube, presentations, TV",
        "  ⬜ Instagram posts, Facebook",
        "",
        "🎉 All existing projects default to 16:9 (landscape)",
        "🎉 New projects can choose any aspect ratio!"
    ]
    
    for line in info:
        print(line)

if __name__ == "__main__":
    print("🎬 Aspect Ratio Migration Script")
    print("=" * 50)
    
    # Run migration
    if migrate_database():
        print("\n✅ Migration completed successfully!")
        
        # Verify migration
        if verify_migration():
            print("\n✅ Migration verified successfully!")
            
            # Show info
            show_aspect_ratio_info()
            
            print(f"\n🌐 Ready to test: http://localhost:5000/create")
            print(f"🎬 Create a new project and try the 9:16 vertical format!")
        else:
            print("\n❌ Migration verification failed")
            sys.exit(1)
    else:
        print("\n❌ Migration failed")
        sys.exit(1)
