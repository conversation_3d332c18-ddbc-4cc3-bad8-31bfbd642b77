#!/usr/bin/env python3
"""
Setup script to configure Pexels API key in the database
"""

import os
import sys

def setup_pexels_api():
    """Setup Pexels API key in the database"""
    try:
        from app import app, db, SystemSettings
        
        with app.app_context():
            # Your Pexels API key
            pexels_api_key = "563492ad6f9170000100000113003a14fe4f413ca1e1d8156475d931"
            
            # Check if setting already exists
            existing_setting = SystemSettings.query.filter_by(key='pexels_api_key').first()
            
            if existing_setting:
                existing_setting.value = pexels_api_key
                print("✅ Updated existing Pexels API key")
            else:
                # Create new setting
                new_setting = SystemSettings(
                    key='pexels_api_key',
                    value=pexels_api_key,
                    description='Pexels API Key for video search',
                    category='video',
                    is_secret=True
                )
                db.session.add(new_setting)
                print("✅ Added new Pexels API key")
            
            # Also set video provider to pexels
            provider_setting = SystemSettings.query.filter_by(key='video_provider').first()
            if provider_setting:
                provider_setting.value = 'pexels'
                print("✅ Set video provider to Pexels")
            else:
                provider_setting = SystemSettings(
                    key='video_provider',
                    value='pexels',
                    description='Primary video provider',
                    category='video',
                    is_secret=False
                )
                db.session.add(provider_setting)
                print("✅ Added video provider setting")
            
            db.session.commit()
            print("🎉 Pexels API configuration completed!")
            
            return True
            
    except Exception as e:
        print(f"❌ Error setting up Pexels API: {e}")
        return False

def test_pexels_api():
    """Test the Pexels API integration"""
    try:
        from ai_services import VideoService
        
        print("\n🧪 Testing Pexels API...")
        
        video_service = VideoService()
        
        # Test search
        videos = video_service.search_videos("technology", per_page=3)
        
        if videos:
            print(f"✅ Pexels API working! Found {len(videos)} videos")
            print("\n🎬 Sample Results:")
            for i, video in enumerate(videos[:2]):
                print(f"  {i+1}. Duration: {video['duration']}s")
                print(f"     Size: {video['width']}x{video['height']}")
                print(f"     Preview: {video['preview']}")
                print(f"     Download: {video['download_url'][:50]}...")
                print()
            return True
        else:
            print("❌ No videos found - check API key")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Pexels API: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Setting up Pexels API Integration")
    print("=" * 50)
    
    # Setup API key
    setup_success = setup_pexels_api()
    
    if setup_success:
        # Test API
        test_success = test_pexels_api()
        
        if test_success:
            print("\n🎉 PEXELS INTEGRATION READY!")
            print("\n📋 What's now available:")
            print("  ✅ Real video search from Pexels")
            print("  ✅ High-quality stock videos")
            print("  ✅ Multiple video formats and sizes")
            print("  ✅ Automatic video download and processing")
            print("\n🚀 Restart the server to use Pexels videos!")
        else:
            print("\n⚠️  Setup completed but API test failed")
    else:
        print("\n❌ Setup failed")
