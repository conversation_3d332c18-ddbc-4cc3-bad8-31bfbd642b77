# 🔑 Demo Credentials - AI Podcast Generator

## 🎯 Quick Access

The AI Podcast Generator comes with pre-configured demo accounts for immediate testing and exploration.

## 👤 User Accounts

### 🛡️ Administrator Account
**Full system access with admin privileges**

- **Username:** `admin`
- **Password:** `admin123`
- **Email:** `<EMAIL>`
- **Access Level:** Full admin privileges
- **Features:**
  - ✅ User management dashboard
  - ✅ System statistics and analytics
  - ✅ Create/manage demo users
  - ✅ View all projects across users
  - ✅ Full application access

**Login URL:** http://localhost:5000/login

---

### 👥 Demo User Accounts
**Standard user accounts for testing features**

#### Demo User 1
- **Username:** `demo_user`
- **Password:** `demo123`
- **Email:** `<EMAIL>`
- **Access Level:** Standard user
- **Features:**
  - ✅ Create podcast projects
  - ✅ Generate AI content (with API keys)
  - ✅ Download final videos
  - ✅ Project management dashboard

#### Demo User 2
- **Username:** `content_creator`
- **Password:** `creator123`
- **Email:** `<EMAIL>`
- **Access Level:** Standard user
- **Features:**
  - ✅ Same as Demo User 1
  - ✅ Separate account for testing multi-user scenarios

**Quick Demo Login:** http://localhost:5000/demo_login (auto-login as demo_user)

---

## 🚀 Quick Start Guide

### Option 1: One-Click Demo
1. Open http://localhost:5000
2. Click **"Try Demo"** button
3. Automatically logged in as `demo_user`
4. Start creating projects immediately!

### Option 2: Manual Login
1. Go to http://localhost:5000/login
2. Enter credentials from above
3. Click "Sign In"
4. Explore the dashboard

### Option 3: Admin Access
1. Go to http://localhost:5000/login
2. Use admin credentials: `admin` / `admin123`
3. Access admin panel via navigation
4. Manage users and view system stats

---

## 🎨 What You Can Test

### 🎙️ Podcast Creation Workflow
1. **Create Project:** Enter keyword, select language, speakers, tone
2. **AI Generation:** Watch real-time content generation (requires API keys)
3. **Video Selection:** Choose from stock videos or generated backgrounds
4. **Final Output:** Download professional podcast videos

### 📊 Admin Features (admin account only)
1. **User Management:** View all registered users
2. **Project Overview:** See all projects across the platform
3. **System Stats:** Monitor usage and performance
4. **Demo Management:** Create additional demo accounts

### 🔧 Interface Features
1. **Responsive Design:** Test on different screen sizes
2. **Real-time Updates:** WebSocket-powered progress tracking
3. **Project Dashboard:** Manage multiple projects
4. **Status Tracking:** Visual progress indicators

---

## 🔑 API Keys (Optional)

To enable full AI functionality, add these API keys to `.env`:

```env
# Required for AI content generation
OPENAI_API_KEY=your_openai_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
PEXELS_API_KEY=your_pexels_api_key_here

# Application security
SECRET_KEY=your_secret_key_here
```

**Without API keys:** Interface and workflow work perfectly, but AI generation will show placeholder content.

**With API keys:** Full AI-powered content generation including:
- ✅ GPT-generated podcast scripts
- ✅ ElevenLabs voice synthesis
- ✅ Pexels stock video integration

---

## 🎯 Testing Scenarios

### Scenario 1: New User Experience
1. Use `demo_user` account
2. Create first project with keyword "Technology"
3. Experience the full workflow
4. Download final video

### Scenario 2: Multi-User Testing
1. Login as `content_creator`
2. Create different projects
3. Switch to `admin` account
4. View all projects in admin panel

### Scenario 3: Admin Management
1. Login as `admin`
2. Access admin dashboard
3. Create additional demo users
4. Monitor system statistics

---

## 🔧 Troubleshooting

### Can't Login?
- Ensure the application is running: `python run.py`
- Check credentials are typed correctly
- Try the one-click demo login

### Demo Users Missing?
- Restart the application to auto-create users
- Or login as admin and use "Create Demo Users" button

### Features Not Working?
- Check if API keys are configured in `.env`
- Some features require external API access
- Interface works fully without API keys

---

## 📱 Mobile Testing

All accounts work perfectly on mobile devices:
- Responsive design adapts to screen size
- Touch-friendly interface
- Full functionality on tablets and phones

---

## 🎉 Ready to Explore!

**🚀 Start here:** http://localhost:5000

Choose your adventure:
- **Quick Demo:** Click "Try Demo" for instant access
- **Admin Power:** Login with admin credentials
- **Full Experience:** Add API keys for complete functionality

**Happy testing!** 🎙️✨
