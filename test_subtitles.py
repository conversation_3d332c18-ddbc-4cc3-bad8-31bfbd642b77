#!/usr/bin/env python3
"""
Test subtitle functionality in the podcast generator
"""

import os
import sys

def test_subtitle_generation():
    """Test the subtitle generation functionality"""
    print("🎬 Testing Subtitle Generation...")
    
    try:
        from app import app
        from video_processor import VideoProcessor
        
        with app.app_context():
            video_processor = VideoProcessor()
            
            # Test script segments (speaker_num, dialogue)
            test_segments = [
                (1, "Bonjour et bienvenue dans notre podcast Triangle."),
                (2, "Merci de m'avoir invitée. C'est un plaisir d'être ici."),
                (1, "Aujourd'hui nous parlons de mystères africains."),
                (2, "Oui, c'est un sujet fascinant et important."),
                (1, "Pouvez-vous nous en dire plus sur votre expérience?"),
                (2, "Bien sûr. J'ai vécu des choses extraordinaires.")
            ]
            
            # Test SRT generation
            audio_duration = 30.0  # 30 seconds
            srt_content = video_processor.generate_srt_from_script(test_segments, audio_duration)
            
            print("✅ Generated SRT content:")
            print("=" * 50)
            print(srt_content)
            print("=" * 50)
            
            # Verify SRT format
            lines = srt_content.strip().split('\n')
            subtitle_count = srt_content.count('\n\n') + 1
            
            print(f"📊 Subtitle Statistics:")
            print(f"  - Total segments: {len(test_segments)}")
            print(f"  - Generated subtitles: {subtitle_count}")
            print(f"  - Audio duration: {audio_duration}s")
            print(f"  - Time per segment: {audio_duration/len(test_segments):.1f}s")
            
            if subtitle_count == len(test_segments):
                print("✅ Subtitle generation working correctly!")
                return True
            else:
                print("❌ Subtitle count mismatch")
                return False
                
    except Exception as e:
        print(f"❌ Error testing subtitles: {e}")
        return False

def create_subtitle_test_project():
    """Create a test project with subtitles enabled"""
    print("\n🎯 Creating Subtitle Test Project...")
    
    try:
        from app import app, db, Project, User
        import uuid
        
        with app.app_context():
            # Get demo user
            demo_user = User.query.filter_by(username='demo').first()
            if not demo_user:
                print("❌ Demo user not found")
                return None
            
            # Create project with subtitles enabled
            test_script = """Speaker 1: Bonjour et bienvenue dans Triangle, votre podcast mystique.
Speaker 2: Merci de m'avoir invitée. Je suis Tata Chocolat.
Speaker 1: Aujourd'hui nous explorons les mystères du Bénin.
Speaker 2: C'est un sujet qui me tient à cœur depuis des années.
Speaker 1: Parlez-nous de votre première expérience mystique.
Speaker 2: Tout a commencé quand j'étais enfant au village."""
            
            project = Project(
                id=str(uuid.uuid4()),
                user_id=demo_user.id,
                title="Test Subtitles - Triangle Mystique",
                content_type='paste',
                keyword='mystique benin',
                custom_script=test_script,
                script=test_script,
                language='fr',
                num_speakers=2,
                tone='conversational',
                auto_create_video=True,
                include_subtitles=True,  # Enable subtitles!
                status='created'
            )
            
            db.session.add(project)
            db.session.commit()
            
            print(f"✅ Created subtitle test project!")
            print(f"📋 Project ID: {project.id}")
            print(f"🎬 Title: {project.title}")
            print(f"📝 Subtitles enabled: {project.include_subtitles}")
            print(f"🌐 URL: http://localhost:5000/project/{project.id}")
            
            return project.id
            
    except Exception as e:
        print(f"❌ Error creating project: {e}")
        return None

def show_subtitle_features():
    """Show the new subtitle features"""
    print("\n" + "="*60)
    print("🎬 SUBTITLE/CAPTION FEATURES")
    print("="*60)
    
    features = [
        "✅ NEW: Subtitle/Caption Support Added!",
        "",
        "🎯 Features:",
        "  ✅ Automatic SRT generation from script",
        "  ✅ Synchronized timing with audio",
        "  ✅ Speaker labels in subtitles",
        "  ✅ Professional subtitle styling",
        "  ✅ Multi-line text for long dialogues",
        "  ✅ French and English support",
        "  ✅ Optional subtitle toggle",
        "",
        "📋 How to use:",
        "  1. Go to: http://localhost:5000/create",
        "  2. Create any project (generate or paste)",
        "  3. ✅ Check 'Include subtitles/captions in video'",
        "  4. Generate your podcast",
        "  5. Video will include synchronized subtitles!",
        "",
        "🎨 Subtitle styling:",
        "  - White text with black outline",
        "  - Bottom-center positioning",
        "  - Readable font size (24px)",
        "  - Speaker labels when multiple speakers",
        "  - Automatic line breaks for long text",
        "",
        "🎬 Video formats supported:",
        "  ✅ Gradient backgrounds",
        "  ✅ Pexels videos",
        "  ✅ Custom uploaded images/videos",
        "  ✅ All with subtitle overlay",
        "",
        "⚙️ Technical details:",
        "  - Uses FFmpeg subtitle filter",
        "  - SRT format generation",
        "  - Timing based on audio duration",
        "  - Embedded in final MP4 file"
    ]
    
    for feature in features:
        print(feature)

def show_subtitle_examples():
    """Show examples of subtitle formats"""
    print("\n" + "="*60)
    print("📝 SUBTITLE FORMAT EXAMPLES")
    print("="*60)
    
    print("🎯 Example SRT output:")
    print("""
1
00:00:00,000 --> 00:00:05,000
Speaker 1: Bonjour et bienvenue dans Triangle,
votre podcast mystique.

2
00:00:05,000 --> 00:00:10,000
Speaker 2: Merci de m'avoir invitée.
Je suis Tata Chocolat.

3
00:00:10,000 --> 00:00:15,000
Speaker 1: Aujourd'hui nous explorons
les mystères du Bénin.
""")
    
    print("🎨 Visual appearance:")
    print("  - Subtitles appear at bottom of video")
    print("  - White text with black outline for readability")
    print("  - Speaker names help identify who's talking")
    print("  - Text automatically wraps for long sentences")

if __name__ == "__main__":
    print("🎬 Subtitle/Caption Feature Test")
    print("=" * 50)
    
    # Test subtitle generation
    subtitle_works = test_subtitle_generation()
    
    if subtitle_works:
        # Create test project
        project_id = create_subtitle_test_project()
        
        if project_id:
            # Show features
            show_subtitle_features()
            show_subtitle_examples()
            
            print(f"\n🎉 SUCCESS! Subtitle feature is ready!")
            print(f"🌐 Test project: http://localhost:5000/project/{project_id}")
            print(f"📋 Create your own subtitled project now!")
        else:
            print("\n⚠️  Subtitle generation works but project creation failed")
    else:
        print("\n❌ Subtitle generation has issues")
        
    print(f"\n🌐 Visit: http://localhost:5000/create to test subtitles!")
