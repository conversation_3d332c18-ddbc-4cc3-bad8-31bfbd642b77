from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_file
import requests
from flask_sqlalchemy import SQLAlchemy
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, UserMixin, login_user, logout_user, login_required, current_user
from flask_wtf import FlaskForm
from flask_socketio import <PERSON><PERSON><PERSON>, emit, join_room
from wtforms import StringField, PasswordField, SelectField, IntegerField, TextAreaField
from wtforms.validators import DataRequired, Email, Length
from werkzeug.security import generate_password_hash, check_password_hash
from dotenv import load_dotenv
import os
import json
from datetime import datetime
from celery import Celery
import uuid

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///podcast_generator.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = os.getenv('UPLOAD_FOLDER', 'uploads')
app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_CONTENT_LENGTH', 16777216))

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize Celery
def make_celery(app):
    celery = Celery(
        app.import_name,
        backend=os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
        broker=os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    )
    celery.conf.update(app.config)
    return celery

celery = make_celery(app)

# Create upload directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'audio'), exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'video'), exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'final'), exist_ok=True)

# Database Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)
    is_demo = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    projects = db.relationship('Project', backref='user', lazy=True)

class SystemSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.String(500))
    category = db.Column(db.String(50))
    is_secret = db.Column(db.Boolean, default=False)  # For API keys
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Project(db.Model):
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    content_type = db.Column(db.String(20), default='generate')  # 'generate' or 'paste'
    keyword = db.Column(db.String(100))  # Optional for pasted content
    custom_script = db.Column(db.Text)  # For user-provided scripts
    language = db.Column(db.String(10), default='en')
    num_speakers = db.Column(db.Integer, default=1)
    tone = db.Column(db.String(50), default='conversational')
    script = db.Column(db.Text)  # Final processed script (AI-generated or user-provided)
    postcard_text = db.Column(db.Text)
    video_url = db.Column(db.String(500))
    audio_file = db.Column(db.String(200))
    final_video = db.Column(db.String(200))
    status = db.Column(db.String(50), default='created')  # created, processing, completed, error
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Forms
class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])

class RegisterForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=4, max=20)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])

class ProjectForm(FlaskForm):
    content_type = SelectField('Content Type', choices=[
        ('generate', 'Generate with AI'),
        ('paste', 'Paste Your Own Text')
    ], default='generate')
    keyword = StringField('Keyword', validators=[Length(min=0, max=100)])
    custom_script = TextAreaField('Your Podcast Script', validators=[Length(min=0, max=5000)])
    language = SelectField('Language', choices=[
        ('en', 'English'),
        ('es', 'Spanish'),
        ('fr', 'French'),
        ('de', 'German'),
        ('it', 'Italian'),
        ('pt', 'Portuguese'),
        ('ja', 'Japanese'),
        ('ko', 'Korean'),
        ('zh', 'Chinese')
    ], default='en')
    num_speakers = SelectField('Number of Speakers', choices=[
        (1, '1 Speaker'),
        (2, '2 Speakers'),
        (3, '3 Speakers')
    ], coerce=int, default=1)
    tone = SelectField('Tone', choices=[
        ('conversational', 'Conversational'),
        ('professional', 'Professional'),
        ('casual', 'Casual'),
        ('educational', 'Educational'),
        ('entertaining', 'Entertaining'),
        ('dramatic', 'Dramatic')
    ], default='conversational')

    def validate(self, **kwargs):
        rv = FlaskForm.validate(self, **kwargs)
        if not rv:
            return False

        # Custom validation based on content type
        if self.content_type.data == 'generate':
            if not self.keyword.data or len(self.keyword.data.strip()) == 0:
                self.keyword.errors.append('Keyword is required for AI generation.')
                return False
        elif self.content_type.data == 'paste':
            if not self.custom_script.data or len(self.custom_script.data.strip()) == 0:
                self.custom_script.errors.append('Script text is required when pasting your own content.')
                return False

        return True

class SettingsForm(FlaskForm):
    # LLM Settings
    llm_provider = SelectField('LLM Provider', choices=[
        ('openai', 'OpenAI GPT'),
        ('anthropic', 'Anthropic Claude'),
        ('google', 'Google Gemini'),
        ('local', 'Local Model')
    ], default='openai')
    openai_api_key = StringField('OpenAI API Key')
    openai_model = SelectField('OpenAI Model', choices=[
        ('gpt-3.5-turbo', 'GPT-3.5 Turbo'),
        ('gpt-4', 'GPT-4'),
        ('gpt-4-turbo', 'GPT-4 Turbo')
    ], default='gpt-3.5-turbo')
    anthropic_api_key = StringField('Anthropic API Key')
    google_api_key = StringField('Google API Key')

    # TTS Settings
    tts_provider = SelectField('TTS Provider', choices=[
        ('elevenlabs', 'ElevenLabs'),
        ('openai', 'OpenAI TTS'),
        ('google', 'Google Cloud TTS'),
        ('azure', 'Azure Cognitive Services')
    ], default='elevenlabs')
    elevenlabs_api_key = StringField('ElevenLabs API Key')
    openai_tts_model = SelectField('OpenAI TTS Model', choices=[
        ('tts-1', 'TTS-1'),
        ('tts-1-hd', 'TTS-1 HD')
    ], default='tts-1')

    # Video Settings
    video_provider = SelectField('Video Provider', choices=[
        ('pexels', 'Pexels'),
        ('unsplash', 'Unsplash'),
        ('pixabay', 'Pixabay'),
        ('generated', 'AI Generated Only')
    ], default='pexels')
    pexels_api_key = StringField('Pexels API Key')
    unsplash_api_key = StringField('Unsplash API Key')
    pixabay_api_key = StringField('Pixabay API Key')

    # System Settings
    max_script_length = SelectField('Max Script Length', choices=[
        ('2000', '2000 characters'),
        ('5000', '5000 characters'),
        ('10000', '10000 characters'),
        ('unlimited', 'Unlimited')
    ], default='5000')
    default_language = SelectField('Default Language', choices=[
        ('en', 'English'),
        ('es', 'Spanish'),
        ('fr', 'French'),
        ('de', 'German'),
        ('it', 'Italian'),
        ('pt', 'Portuguese'),
        ('ja', 'Japanese'),
        ('ko', 'Korean'),
        ('zh', 'Chinese')
    ], default='en')
    enable_background_music = SelectField('Background Music', choices=[
        ('true', 'Enabled'),
        ('false', 'Disabled')
    ], default='true')

# Helper functions for settings
def get_setting(key, default=None):
    """Get a system setting value"""
    setting = SystemSettings.query.filter_by(key=key).first()
    return setting.value if setting else default

def set_setting(key, value, description=None, category=None, is_secret=False):
    """Set a system setting value"""
    setting = SystemSettings.query.filter_by(key=key).first()
    if setting:
        setting.value = value
        setting.updated_at = datetime.utcnow()
    else:
        setting = SystemSettings(
            key=key,
            value=value,
            description=description,
            category=category,
            is_secret=is_secret
        )
        db.session.add(setting)
    db.session.commit()

def initialize_default_settings():
    """Initialize default system settings"""
    default_settings = [
        # LLM Settings
        ('llm_provider', 'openai', 'Primary LLM provider for content generation', 'llm', False),
        ('openai_model', 'gpt-3.5-turbo', 'OpenAI model to use', 'llm', False),

        # TTS Settings
        ('tts_provider', 'elevenlabs', 'Primary TTS provider', 'tts', False),
        ('openai_tts_model', 'tts-1', 'OpenAI TTS model', 'tts', False),

        # Video Settings
        ('video_provider', 'pexels', 'Primary video provider', 'video', False),

        # System Settings
        ('max_script_length', '5000', 'Maximum script length in characters', 'system', False),
        ('default_language', 'en', 'Default language for new projects', 'system', False),
        ('enable_background_music', 'true', 'Enable background music feature', 'system', False),
    ]

    for key, value, description, category, is_secret in default_settings:
        if not SystemSettings.query.filter_by(key=key).first():
            setting = SystemSettings(
                key=key,
                value=value,
                description=description,
                category=category,
                is_secret=is_secret
            )
            db.session.add(setting)

    db.session.commit()

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = RegisterForm()
    if form.validate_on_submit():
        # Check if user already exists
        existing_user = User.query.filter_by(username=form.username.data).first()
        if existing_user:
            flash('Username already exists')
            return render_template('register.html', form=form)

        existing_email = User.query.filter_by(email=form.email.data).first()
        if existing_email:
            flash('Email already registered')
            return render_template('register.html', form=form)

        # Create new user
        user = User(
            username=form.username.data,
            email=form.email.data,
            password_hash=generate_password_hash(form.password.data)
        )
        db.session.add(user)
        db.session.commit()

        login_user(user)
        flash('Registration successful!')
        return redirect(url_for('dashboard'))

    return render_template('register.html', form=form)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and check_password_hash(user.password_hash, form.password.data):
            login_user(user)
            return redirect(url_for('dashboard'))
        flash('Invalid username or password')

    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    projects = Project.query.filter_by(user_id=current_user.id).order_by(Project.created_at.desc()).all()
    return render_template('dashboard.html', projects=projects)

@app.route('/create', methods=['GET', 'POST'])
@login_required
def create_project():
    form = ProjectForm()
    if form.validate_on_submit():
        # Create title based on content type
        if form.content_type.data == 'generate':
            title = f"Podcast about {form.keyword.data}"
        else:
            # Extract first few words from custom script for title
            script_words = form.custom_script.data.strip().split()[:5]
            title = f"Custom Podcast: {' '.join(script_words)}..."

        project = Project(
            user_id=current_user.id,
            title=title,
            content_type=form.content_type.data,
            keyword=form.keyword.data if form.content_type.data == 'generate' else None,
            custom_script=form.custom_script.data if form.content_type.data == 'paste' else None,
            language=form.language.data,
            num_speakers=form.num_speakers.data,
            tone=form.tone.data
        )
        db.session.add(project)
        db.session.commit()

        if form.content_type.data == 'generate':
            # Start AI generation background task
            from tasks import generate_podcast_content
            generate_podcast_content.delay(
                project.id,
                form.keyword.data,
                form.language.data,
                form.num_speakers.data,
                form.tone.data
            )
        else:
            # Process pasted content immediately
            from tasks import process_custom_content
            process_custom_content.delay(
                project.id,
                form.custom_script.data,
                form.language.data,
                form.num_speakers.data,
                form.tone.data
            )

        return redirect(url_for('project_detail', project_id=project.id))

    return render_template('create_project.html', form=form)

@app.route('/project/<project_id>')
@login_required
def project_detail(project_id):
    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first_or_404()
    return render_template('project_detail.html', project=project)

@app.route('/api/generate_video', methods=['POST'])
@login_required
def generate_video():
    data = request.get_json()
    project_id = data.get('project_id')
    video_url = data.get('video_url')
    background_music = data.get('background_music')

    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
    if not project:
        return jsonify({'error': 'Project not found'}), 404

    # Start video generation task
    from tasks import generate_final_video
    generate_final_video.delay(project_id, video_url, background_music)

    return jsonify({'status': 'started'})

@app.route('/download/<project_id>')
@login_required
def download_video(project_id):
    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first_or_404()

    if not project.final_video:
        flash('Video not ready yet')
        return redirect(url_for('project_detail', project_id=project_id))

    video_path = os.path.join(app.config['UPLOAD_FOLDER'], 'final', project.final_video)
    if not os.path.exists(video_path):
        flash('Video file not found')
        return redirect(url_for('project_detail', project_id=project_id))

    return send_file(video_path, as_attachment=True, download_name=f"{project.title}.mp4")

@app.route('/api/project/<project_id>/status')
@login_required
def project_status(project_id):
    project = Project.query.filter_by(id=project_id, user_id=current_user.id).first_or_404()
    return jsonify({
        'status': project.status,
        'script': project.script,
        'postcard_text': project.postcard_text,
        'audio_file': project.audio_file,
        'final_video': project.final_video
    })

# Admin Routes
@app.route('/admin')
@login_required
def admin_dashboard():
    if not current_user.is_admin:
        flash('Access denied. Admin privileges required.')
        return redirect(url_for('dashboard'))

    users = User.query.all()
    projects = Project.query.all()

    stats = {
        'total_users': len(users),
        'total_projects': len(projects),
        'completed_projects': len([p for p in projects if p.status == 'completed']),
        'demo_users': len([u for u in users if u.is_demo])
    }

    return render_template('admin_dashboard.html', users=users, projects=projects, stats=stats)

@app.route('/admin/create_demo_users')
@login_required
def create_demo_users():
    if not current_user.is_admin:
        flash('Access denied. Admin privileges required.')
        return redirect(url_for('dashboard'))

    # Create demo users if they don't exist
    demo_users_created = []

    # Demo User 1
    if not User.query.filter_by(username='demo_user').first():
        demo_user = User(
            username='demo_user',
            email='<EMAIL>',
            password_hash=generate_password_hash('demo123'),
            is_demo=True
        )
        db.session.add(demo_user)
        demo_users_created.append('demo_user')

    # Demo User 2
    if not User.query.filter_by(username='content_creator').first():
        creator_user = User(
            username='content_creator',
            email='<EMAIL>',
            password_hash=generate_password_hash('creator123'),
            is_demo=True
        )
        db.session.add(creator_user)
        demo_users_created.append('content_creator')

    db.session.commit()

    if demo_users_created:
        flash(f'Demo users created: {", ".join(demo_users_created)}')
    else:
        flash('Demo users already exist')

    return redirect(url_for('admin_dashboard'))

@app.route('/admin/settings', methods=['GET', 'POST'])
@login_required
def admin_settings():
    if not current_user.is_admin:
        flash('Access denied. Admin privileges required.')
        return redirect(url_for('dashboard'))

    form = SettingsForm()

    if form.validate_on_submit():
        # Save all settings
        settings_to_save = [
            # LLM Settings
            ('llm_provider', form.llm_provider.data, 'Primary LLM provider', 'llm', False),
            ('openai_api_key', form.openai_api_key.data, 'OpenAI API Key', 'llm', True),
            ('openai_model', form.openai_model.data, 'OpenAI model', 'llm', False),
            ('anthropic_api_key', form.anthropic_api_key.data, 'Anthropic API Key', 'llm', True),
            ('google_api_key', form.google_api_key.data, 'Google API Key', 'llm', True),

            # TTS Settings
            ('tts_provider', form.tts_provider.data, 'Primary TTS provider', 'tts', False),
            ('elevenlabs_api_key', form.elevenlabs_api_key.data, 'ElevenLabs API Key', 'tts', True),
            ('openai_tts_model', form.openai_tts_model.data, 'OpenAI TTS model', 'tts', False),

            # Video Settings
            ('video_provider', form.video_provider.data, 'Primary video provider', 'video', False),
            ('pexels_api_key', form.pexels_api_key.data, 'Pexels API Key', 'video', True),
            ('unsplash_api_key', form.unsplash_api_key.data, 'Unsplash API Key', 'video', True),
            ('pixabay_api_key', form.pixabay_api_key.data, 'Pixabay API Key', 'video', True),

            # System Settings
            ('max_script_length', form.max_script_length.data, 'Max script length', 'system', False),
            ('default_language', form.default_language.data, 'Default language', 'system', False),
            ('enable_background_music', form.enable_background_music.data, 'Background music', 'system', False),
        ]

        for key, value, description, category, is_secret in settings_to_save:
            if value:  # Only save non-empty values
                set_setting(key, value, description, category, is_secret)

        flash('Settings saved successfully!')
        return redirect(url_for('admin_settings'))

    # Load current settings into form
    form.llm_provider.data = get_setting('llm_provider', 'openai')
    form.openai_api_key.data = get_setting('openai_api_key', '')
    form.openai_model.data = get_setting('openai_model', 'gpt-3.5-turbo')
    form.anthropic_api_key.data = get_setting('anthropic_api_key', '')
    form.google_api_key.data = get_setting('google_api_key', '')

    form.tts_provider.data = get_setting('tts_provider', 'elevenlabs')
    form.elevenlabs_api_key.data = get_setting('elevenlabs_api_key', '')
    form.openai_tts_model.data = get_setting('openai_tts_model', 'tts-1')

    form.video_provider.data = get_setting('video_provider', 'pexels')
    form.pexels_api_key.data = get_setting('pexels_api_key', '')
    form.unsplash_api_key.data = get_setting('unsplash_api_key', '')
    form.pixabay_api_key.data = get_setting('pixabay_api_key', '')

    form.max_script_length.data = get_setting('max_script_length', '5000')
    form.default_language.data = get_setting('default_language', 'en')
    form.enable_background_music.data = get_setting('enable_background_music', 'true')

    # Get all settings for display
    all_settings = SystemSettings.query.order_by(SystemSettings.category, SystemSettings.key).all()

    return render_template('admin_settings.html', form=form, settings=all_settings)

@app.route('/admin/settings/test', methods=['POST'])
@login_required
def test_api_settings():
    if not current_user.is_admin:
        return jsonify({'error': 'Access denied'}), 403

    data = request.get_json()
    provider = data.get('provider')
    api_key = data.get('api_key')

    try:
        if provider == 'openai':
            import openai
            client = openai.OpenAI(api_key=api_key)
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Test"}],
                max_tokens=5
            )
            return jsonify({'status': 'success', 'message': 'OpenAI API key is valid'})

        elif provider == 'elevenlabs':
            headers = {'xi-api-key': api_key}
            response = requests.get('https://api.elevenlabs.io/v1/voices', headers=headers)
            if response.status_code == 200:
                return jsonify({'status': 'success', 'message': 'ElevenLabs API key is valid'})
            else:
                return jsonify({'status': 'error', 'message': 'Invalid ElevenLabs API key'})

        elif provider == 'pexels':
            headers = {'Authorization': api_key}
            response = requests.get('https://api.pexels.com/v1/search?query=test&per_page=1', headers=headers)
            if response.status_code == 200:
                return jsonify({'status': 'success', 'message': 'Pexels API key is valid'})
            else:
                return jsonify({'status': 'error', 'message': 'Invalid Pexels API key'})

        else:
            return jsonify({'status': 'error', 'message': 'Unknown provider'})

    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/demo_login')
def demo_login():
    """Quick demo login without password"""
    demo_user = User.query.filter_by(username='demo_user').first()
    if demo_user:
        login_user(demo_user)
        flash('Logged in as demo user!')
        return redirect(url_for('dashboard'))
    else:
        flash('Demo user not found. Please contact admin.')
        return redirect(url_for('login'))

# SocketIO events
@socketio.on('connect')
def handle_connect():
    if current_user.is_authenticated:
        emit('connected', {'message': 'Connected to server'})

@socketio.on('join_project')
def handle_join_project(data):
    if current_user.is_authenticated:
        project_id = data.get('project_id')
        project = Project.query.filter_by(id=project_id, user_id=current_user.id).first()
        if project:
            join_room(project_id)
            emit('joined_project', {'project_id': project_id})

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
