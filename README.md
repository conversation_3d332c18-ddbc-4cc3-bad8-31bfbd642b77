# 🎙️ AI Podcast Generator

A cutting-edge Flask application that combines multiple AI technologies to create engaging podcast content with professional voiceovers and stunning visuals. Transform any keyword into a complete podcast video in minutes!

## ✨ Features

### 🤖 AI-Powered Content Generation
- **Dual Content Modes**: AI generation from keywords OR paste your own script
- **Smart Script Writing**: Uses OpenAI GPT to generate engaging podcast scripts
- **Custom Script Support**: Paste existing content with intelligent multi-speaker parsing
- **Multi-Speaker Support**: Natural conversations between 1-3 speakers with distinct voices
- **Postcard Text**: Generates shareable social media content
- **Multiple Languages**: Support for 9 languages including English, Spanish, French, German, Italian, Portuguese, Japanese, Korean, and Chinese

### 🎵 Professional Audio Generation
- **Text-to-Speech**: High-quality voice synthesis using ElevenLabs
- **Multiple Voices**: Different voices for each speaker
- **Natural Dialogue**: SSML support for natural-sounding conversations
- **Audio Processing**: Normalization and fade effects

### 🎬 Video Integration
- **Stock Video Search**: Integration with Pexels API for professional footage
- **Background Generation**: AI-generated backgrounds when stock videos aren't available
- **Video Processing**: Automatic audio-video synchronization using FFmpeg
- **Background Music**: Optional background music integration

### 🌐 Modern Web Interface
- **Responsive Design**: Beautiful, mobile-friendly interface
- **Real-time Updates**: WebSocket integration for live progress tracking
- **User Management**: Secure authentication and project management
- **Dashboard**: Comprehensive project overview and management

### ⚡ Advanced Processing
- **Background Tasks**: Celery integration for long-running processes
- **Queue Management**: Redis-based task queue
- **Progress Tracking**: Real-time status updates
- **Error Handling**: Robust error recovery and retry mechanisms

## 🛠️ Technology Stack

### Backend
- **Flask**: Web framework with SQLAlchemy ORM
- **SQLite**: Database for development (easily upgradeable to PostgreSQL)
- **Celery**: Distributed task queue for background processing
- **Redis**: Message broker and caching
- **Flask-SocketIO**: Real-time communication

### AI Services
- **OpenAI GPT**: Content generation and script writing
- **ElevenLabs**: Professional text-to-speech synthesis
- **Pexels API**: Stock video and image search

### Media Processing
- **FFmpeg**: Video and audio processing
- **MoviePy**: Python video editing library
- **Pydub**: Audio manipulation

### Frontend
- **Bootstrap 5**: Modern, responsive UI framework
- **Font Awesome**: Professional icons
- **Socket.IO**: Real-time client-server communication
- **Vanilla JavaScript**: Clean, efficient client-side code

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Redis server
- FFmpeg installed on your system

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd podcast-generator
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` and add your API keys:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
   PEXELS_API_KEY=your_pexels_api_key_here
   SECRET_KEY=your_secret_key_here
   ```

4. **Start Redis server**
   ```bash
   redis-server
   ```

5. **Start Celery worker** (in a new terminal)
   ```bash
   celery -A tasks worker --loglevel=info
   ```

6. **Run the application**
   ```bash
   python run.py
   ```

7. **Open your browser**
   Navigate to `http://localhost:5000`

## 📖 How It Works

### 1. Content Generation
1. User enters a keyword and selects preferences (language, speakers, tone)
2. OpenAI GPT generates an engaging podcast script
3. The system creates postcard text for social sharing
4. ElevenLabs converts the script to natural-sounding audio with multiple voices

### 2. Video Creation
1. Pexels API searches for relevant stock videos
2. User selects their preferred video
3. FFmpeg combines the audio with the selected video
4. Optional background music is mixed in
5. Final MP4 video is generated and ready for download

### 3. Real-time Processing
- WebSocket connections provide live updates
- Background tasks handle time-intensive operations
- Progress bars show current processing stage
- Users can continue browsing while content generates

## 🎯 Use Cases

- **Content Creators**: Generate podcast content quickly
- **Educators**: Create educational audio-visual content
- **Marketers**: Produce engaging social media content
- **Businesses**: Create professional presentations
- **Podcasters**: Prototype new episode ideas

## 🔧 Configuration

### Environment Variables
- `OPENAI_API_KEY`: Your OpenAI API key
- `ELEVENLABS_API_KEY`: Your ElevenLabs API key
- `PEXELS_API_KEY`: Your Pexels API key
- `SECRET_KEY`: Flask secret key for sessions
- `REDIS_URL`: Redis connection URL (default: redis://localhost:6379/0)
- `DATABASE_URL`: Database URL (default: sqlite:///podcast_generator.db)

### Customization
- **Voices**: Modify voice mappings in `ai_services.py`
- **Languages**: Add new languages in the form choices
- **Video Sources**: Integrate additional stock video APIs
- **Audio Effects**: Customize audio processing in `video_processor.py`

## 📁 Project Structure

```
podcast-generator/
├── app.py                 # Main Flask application
├── ai_services.py         # AI service integrations
├── video_processor.py     # Video/audio processing
├── tasks.py              # Celery background tasks
├── celeryconfig.py       # Celery configuration
├── run.py                # Application startup script
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables template
├── templates/           # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── dashboard.html
│   ├── create_project.html
│   └── project_detail.html
└── uploads/             # Generated files
    ├── audio/
    ├── video/
    └── final/
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues or have questions:
1. Check the troubleshooting section below
2. Review the error logs
3. Open an issue on GitHub

## 🔧 Troubleshooting

### Common Issues

**Redis Connection Error**
- Ensure Redis server is running: `redis-server`
- Check Redis URL in environment variables

**FFmpeg Not Found**
- Install FFmpeg: `brew install ffmpeg` (macOS) or `apt install ffmpeg` (Ubuntu)
- Ensure FFmpeg is in your system PATH

**API Key Errors**
- Verify all API keys are correctly set in `.env`
- Check API key permissions and quotas

**Celery Worker Issues**
- Restart Celery worker: `celery -A tasks worker --loglevel=info`
- Check Redis connection

## 🎉 Acknowledgments

- OpenAI for GPT API
- ElevenLabs for voice synthesis
- Pexels for stock video API
- The Flask and Python communities

---

**Ready to create amazing AI-powered podcasts? Get started now!** 🚀
