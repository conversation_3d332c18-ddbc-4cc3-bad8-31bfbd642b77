#!/usr/bin/env python3
"""
Quick fix to set up API keys properly
"""

import os
import sys

def setup_demo_mode():
    """Set up demo mode with working API keys"""
    print("🔧 Setting up Demo Mode...")
    
    try:
        from app import app, db, SystemSettings
        
        with app.app_context():
            # Set up a demo OpenAI API key (you'll need to replace this with a real one)
            demo_openai_key = "sk-demo-key-replace-with-real-openai-key"
            
            print("⚠️  IMPORTANT: You need a real OpenAI API key to use AI features!")
            print("🌐 Get your API key from: https://platform.openai.com/account/api-keys")
            print()
            
            # Ask user for their OpenAI API key
            user_key = input("🔑 Enter your OpenAI API key (or press Enter to skip): ").strip()
            
            if user_key and user_key.startswith('sk-'):
                openai_key = user_key
                print("✅ Using your OpenAI API key")
            else:
                print("⚠️  No valid OpenAI API key provided")
                print("💡 You can add it later by running: python setup_openai_key.py")
                openai_key = demo_openai_key
            
            # Set OpenAI API key
            openai_setting = SystemSettings.query.filter_by(key='openai_api_key').first()
            if openai_setting:
                openai_setting.value = openai_key
            else:
                openai_setting = SystemSettings(
                    key='openai_api_key',
                    value=openai_key,
                    description='OpenAI API Key for text generation and TTS',
                    category='ai',
                    is_secret=True
                )
                db.session.add(openai_setting)
            
            # Ensure Pexels API key is set (already working)
            pexels_setting = SystemSettings.query.filter_by(key='pexels_api_key').first()
            if not pexels_setting:
                pexels_setting = SystemSettings(
                    key='pexels_api_key',
                    value='563492ad6f9170000100000113003a14fe4f413ca1e1d8156475d931',
                    description='Pexels API Key for video search',
                    category='video',
                    is_secret=True
                )
                db.session.add(pexels_setting)
            
            # Set TTS provider to OpenAI (since it works with OpenAI API key)
            tts_setting = SystemSettings.query.filter_by(key='tts_provider').first()
            if tts_setting:
                tts_setting.value = 'openai'
            else:
                tts_setting = SystemSettings(
                    key='tts_provider',
                    value='openai',
                    description='Primary TTS provider',
                    category='tts',
                    is_secret=False
                )
                db.session.add(tts_setting)
            
            db.session.commit()
            print("✅ API keys configured in database")
            
            return True
            
    except Exception as e:
        print(f"❌ Error setting up demo mode: {e}")
        return False

def test_configuration():
    """Test the current configuration"""
    print("\n🧪 Testing Configuration...")
    
    try:
        from app import app, SystemSettings
        
        with app.app_context():
            # Check API keys
            openai_setting = SystemSettings.query.filter_by(key='openai_api_key').first()
            pexels_setting = SystemSettings.query.filter_by(key='pexels_api_key').first()
            tts_setting = SystemSettings.query.filter_by(key='tts_provider').first()
            
            if openai_setting and openai_setting.value:
                if openai_setting.value.startswith('sk-') and 'demo' not in openai_setting.value:
                    print("✅ OpenAI API key: Real key configured")
                else:
                    print("⚠️  OpenAI API key: Demo/placeholder key")
            else:
                print("❌ OpenAI API key: Not configured")
            
            if pexels_setting and pexels_setting.value:
                print("✅ Pexels API key: Configured")
            else:
                print("❌ Pexels API key: Not configured")
            
            if tts_setting and tts_setting.value:
                print(f"✅ TTS provider: {tts_setting.value}")
            else:
                print("❌ TTS provider: Not configured")
            
            return True
            
    except Exception as e:
        print(f"❌ Error testing configuration: {e}")
        return False

def create_test_project():
    """Create a test project to verify everything works"""
    print("\n🎯 Creating Test Project...")
    
    try:
        from app import app, db, Project, User
        import uuid
        
        with app.app_context():
            # Get or create demo user
            demo_user = User.query.filter_by(username='demo').first()
            if not demo_user:
                print("❌ Demo user not found. Please login first.")
                return False
            
            # Create test project
            test_project = Project(
                id=str(uuid.uuid4()),
                user_id=demo_user.id,
                title="Test Project - API Keys",
                content_type='paste',
                custom_script="Hello, this is a test podcast. We are testing the AI podcast generator with multiple speakers. This should work with the configured API keys.",
                language='en',
                num_speakers=2,
                tone='conversational',
                auto_create_video=False,
                status='created'
            )
            
            db.session.add(test_project)
            db.session.commit()
            
            print(f"✅ Test project created: {test_project.id}")
            print(f"🌐 Visit: http://localhost:5000/project/{test_project.id}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error creating test project: {e}")
        return False

def show_instructions():
    """Show final instructions"""
    print("\n" + "="*60)
    print("🚀 SETUP COMPLETE!")
    print("="*60)
    
    instructions = [
        "1. 🔄 Restart the server:",
        "   - Press Ctrl+C in the terminal running the server",
        "   - Run: python run.py",
        "",
        "2. 🌐 Open your browser:",
        "   - Visit: http://localhost:5000",
        "   - Click 'Demo Login' to access the system",
        "",
        "3. 🎯 Test the features:",
        "   - Create a new project",
        "   - Try both AI generation and custom script",
        "   - Test video creation with Pexels videos",
        "",
        "4. 🔑 For full functionality:",
        "   - Get a real OpenAI API key from:",
        "     https://platform.openai.com/account/api-keys",
        "   - Run: python setup_openai_key.py",
        "",
        "5. ✨ Available features:",
        "   - ✅ Pexels video integration (working)",
        "   - ✅ French language support",
        "   - ✅ Auto video creation",
        "   - ✅ Delete projects",
        "   - ⚠️  AI generation (needs real OpenAI key)"
    ]
    
    for instruction in instructions:
        print(instruction)

if __name__ == "__main__":
    print("🔧 Quick Fix - API Keys Setup")
    print("=" * 50)
    
    # Setup demo mode
    setup_success = setup_demo_mode()
    
    if setup_success:
        # Test configuration
        test_configuration()
        
        # Create test project
        create_test_project()
        
        # Show instructions
        show_instructions()
    else:
        print("\n❌ Setup failed")
        print("💡 Try running the diagnostic script: python diagnose_error.py")
