import os
import subprocess
import tempfile
from typing import Optional
import logging

# Try to import MoviePy, handle gracefully if not available
try:
    from moviepy.editor import VideoFileClip, AudioFileClip, CompositeVideoClip, concatenate_videoclips, CompositeAudioClip
    from moviepy.audio.fx import volumex
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("⚠️  MoviePy not available. Video processing will use FFmpeg fallback.")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VideoProcessor:
    """Handles video processing, audio synchronization, and final video generation"""

    def __init__(self):
        self.temp_dir = tempfile.gettempdir()

    def combine_audio_video(self, video_path: str, audio_path: str, output_path: str,
                          background_music_path: Optional[str] = None) -> bool:
        """Combine audio with video, optionally adding background music"""

        if MOVIEPY_AVAILABLE:
            return self._combine_with_moviepy(video_path, audio_path, output_path, background_music_path)
        else:
            return self._combine_with_ffmpeg(video_path, audio_path, output_path, background_music_path)

    def _combine_with_moviepy(self, video_path: str, audio_path: str, output_path: str,
                            background_music_path: Optional[str] = None) -> bool:
        """Combine audio with video using MoviePy"""
        try:
            # Load video and audio
            video = VideoFileClip(video_path)
            audio = AudioFileClip(audio_path)

            # Get audio duration
            audio_duration = audio.duration

            # Adjust video to match audio duration
            if video.duration < audio_duration:
                # Loop video if it's shorter than audio
                loops_needed = int(audio_duration / video.duration) + 1
                video = concatenate_videoclips([video] * loops_needed)

            # Trim video to match audio duration
            video = video.subclip(0, audio_duration)

            # Set the audio to the video
            final_video = video.set_audio(audio)

            # Add background music if provided
            if background_music_path and os.path.exists(background_music_path):
                background_music = AudioFileClip(background_music_path)

                # Loop background music if needed
                if background_music.duration < audio_duration:
                    loops_needed = int(audio_duration / background_music.duration) + 1
                    background_music = concatenate_videoclips([background_music] * loops_needed)

                # Trim background music to match duration
                background_music = background_music.subclip(0, audio_duration)

                # Reduce background music volume
                background_music = background_music.fx(volumex, 0.3)

                # Combine voice audio with background music
                combined_audio = CompositeAudioClip([audio, background_music])
                final_video = final_video.set_audio(combined_audio)

            # Write the final video
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # Clean up
            video.close()
            audio.close()
            if background_music_path and os.path.exists(background_music_path):
                background_music.close()
            final_video.close()

            return True

        except Exception as e:
            logger.error(f"Error combining audio and video with MoviePy: {e}")
            return False

    def _combine_with_ffmpeg(self, video_path: str, audio_path: str, output_path: str,
                           background_music_path: Optional[str] = None) -> bool:
        """Combine audio with video using FFmpeg directly"""
        try:
            # Get audio duration first
            audio_info_cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', audio_path
            ]

            result = subprocess.run(audio_info_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("Failed to get audio duration")
                return False

            import json
            audio_info = json.loads(result.stdout)
            audio_duration = float(audio_info['format']['duration'])

            # Basic video + audio combination
            if background_music_path and os.path.exists(background_music_path):
                # Complex command with background music
                cmd = [
                    'ffmpeg', '-y',
                    '-stream_loop', '-1', '-i', video_path,  # Loop video
                    '-i', audio_path,  # Main audio
                    '-stream_loop', '-1', '-i', background_music_path,  # Background music
                    '-filter_complex',
                    f'[0:v]trim=duration={audio_duration}[v];'
                    f'[2:a]volume=0.3[bg];'
                    f'[1:a][bg]amix=inputs=2:duration=first[a]',
                    '-map', '[v]', '-map', '[a]',
                    '-c:v', 'libx264', '-c:a', 'aac',
                    '-shortest',
                    output_path
                ]
            else:
                # Simple command without background music
                cmd = [
                    'ffmpeg', '-y',
                    '-stream_loop', '-1', '-i', video_path,  # Loop video
                    '-i', audio_path,  # Main audio
                    '-filter_complex',
                    f'[0:v]trim=duration={audio_duration}[v]',
                    '-map', '[v]', '-map', '1:a',
                    '-c:v', 'libx264', '-c:a', 'aac',
                    '-shortest',
                    output_path
                ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info("Successfully combined audio and video with FFmpeg")
                return True
            else:
                logger.error(f"FFmpeg error: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error combining audio and video with FFmpeg: {e}")
            return False

    def add_subtitles(self, video_path: str, subtitle_text: str, output_path: str) -> bool:
        """Add subtitles to video using FFmpeg"""
        try:
            # Create a temporary subtitle file
            subtitle_file = os.path.join(self.temp_dir, f"temp_subtitles_{os.getpid()}.srt")

            # Generate simple SRT content
            # This is a basic implementation - in production, you'd want proper timing
            srt_content = f"""1
00:00:00,000 --> 00:00:10,000
{subtitle_text[:100]}...

2
00:00:10,000 --> 00:00:20,000
{subtitle_text[100:200] if len(subtitle_text) > 100 else ''}...
"""

            with open(subtitle_file, 'w', encoding='utf-8') as f:
                f.write(srt_content)

            # Use FFmpeg to add subtitles
            cmd = [
                'ffmpeg',
                '-i', video_path,
                '-vf', f"subtitles={subtitle_file}",
                '-c:a', 'copy',
                '-y',  # Overwrite output file
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            # Clean up temporary subtitle file
            if os.path.exists(subtitle_file):
                os.remove(subtitle_file)

            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error adding subtitles: {e}")
            return False

    def create_simple_background_video(self, duration: float, output_path: str,
                                     style: str = "gradient") -> bool:
        """Create a simple background video when no stock video is available"""
        try:
            if style == "gradient":
                # Create a gradient background using FFmpeg
                cmd = [
                    'ffmpeg',
                    '-f', 'lavfi',
                    '-i', f'color=c=0x1e3a8a:size=1920x1080:duration={duration}',
                    '-vf', 'fade=in:0:30,fade=out:st={duration-1}:d=1'.format(duration=duration),
                    '-c:v', 'libx264',
                    '-pix_fmt', 'yuv420p',
                    '-y',
                    output_path
                ]
            elif style == "particles":
                # Create a particle effect background
                cmd = [
                    'ffmpeg',
                    '-f', 'lavfi',
                    '-i', f'color=c=black:size=1920x1080:duration={duration}',
                    '-vf', f'geq=random(1)*255:128:128,scale=1920:1080',
                    '-c:v', 'libx264',
                    '-pix_fmt', 'yuv420p',
                    '-y',
                    output_path
                ]
            else:
                # Default solid color
                cmd = [
                    'ffmpeg',
                    '-f', 'lavfi',
                    '-i', f'color=c=darkblue:size=1920x1080:duration={duration}',
                    '-c:v', 'libx264',
                    '-pix_fmt', 'yuv420p',
                    '-y',
                    output_path
                ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error creating background: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error creating background video: {e}")
            return False

    def get_video_info(self, video_path: str) -> dict:
        """Get video information using FFprobe"""
        try:
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                import json
                info = json.loads(result.stdout)

                video_stream = None
                for stream in info.get('streams', []):
                    if stream.get('codec_type') == 'video':
                        video_stream = stream
                        break

                if video_stream:
                    return {
                        'duration': float(info['format']['duration']),
                        'width': video_stream['width'],
                        'height': video_stream['height'],
                        'fps': eval(video_stream.get('r_frame_rate', '30/1'))
                    }

            return {}

        except Exception as e:
            logger.error(f"Error getting video info: {e}")
            return {}

    def resize_video(self, input_path: str, output_path: str, width: int = 1920, height: int = 1080) -> bool:
        """Resize video to specified dimensions"""
        try:
            cmd = [
                'ffmpeg',
                '-i', input_path,
                '-vf', f'scale={width}:{height}:force_original_aspect_ratio=decrease,pad={width}:{height}:(ow-iw)/2:(oh-ih)/2',
                '-c:a', 'copy',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error resizing video: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error resizing video: {e}")
            return False

class AudioProcessor:
    """Handles audio processing and enhancement"""

    def __init__(self):
        pass

    def normalize_audio(self, input_path: str, output_path: str) -> bool:
        """Normalize audio levels"""
        try:
            cmd = [
                'ffmpeg',
                '-i', input_path,
                '-af', 'loudnorm',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error normalizing audio: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error normalizing audio: {e}")
            return False

    def add_fade_effects(self, input_path: str, output_path: str, fade_in: float = 1.0, fade_out: float = 1.0) -> bool:
        """Add fade in/out effects to audio"""
        try:
            cmd = [
                'ffmpeg',
                '-i', input_path,
                '-af', f'afade=in:st=0:d={fade_in},afade=out:st=end-{fade_out}:d={fade_out}',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error adding fade effects: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error adding fade effects: {e}")
            return False
