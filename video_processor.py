import os
import subprocess
import tempfile
from typing import Optional, List, Tuple
import logging
import re
import requests
import time

# Try to import MoviePy, handle gracefully if not available
try:
    from moviepy.editor import VideoFileClip, AudioFileClip, CompositeVideoClip, concatenate_videoclips, CompositeAudioClip
    from moviepy.audio.fx import volumex
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("⚠️  MoviePy not available. Video processing will use FFmpeg fallback.")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VideoProcessor:
    """Handles video processing, audio synchronization, and final video generation"""

    def __init__(self):
        self.temp_dir = tempfile.gettempdir()

    def combine_audio_video(self, video_path: str, audio_path: str, output_path: str,
                          background_music_path: Optional[str] = None) -> bool:
        """Combine audio with video, optionally adding background music"""

        if MOVIEPY_AVAILABLE:
            return self._combine_with_moviepy(video_path, audio_path, output_path, background_music_path)
        else:
            return self._combine_with_ffmpeg(video_path, audio_path, output_path, background_music_path)

    def _combine_with_moviepy(self, video_path: str, audio_path: str, output_path: str,
                            background_music_path: Optional[str] = None) -> bool:
        """Combine audio with video using MoviePy"""
        try:
            # Load video and audio
            video = VideoFileClip(video_path)
            audio = AudioFileClip(audio_path)

            # Get audio duration
            audio_duration = audio.duration

            # Adjust video to match audio duration
            if video.duration < audio_duration:
                # Loop video if it's shorter than audio
                loops_needed = int(audio_duration / video.duration) + 1
                video = concatenate_videoclips([video] * loops_needed)

            # Trim video to match audio duration
            video = video.subclip(0, audio_duration)

            # Set the audio to the video
            final_video = video.set_audio(audio)

            # Add background music if provided
            if background_music_path and os.path.exists(background_music_path):
                background_music = AudioFileClip(background_music_path)

                # Loop background music if needed
                if background_music.duration < audio_duration:
                    loops_needed = int(audio_duration / background_music.duration) + 1
                    background_music = concatenate_videoclips([background_music] * loops_needed)

                # Trim background music to match duration
                background_music = background_music.subclip(0, audio_duration)

                # Reduce background music volume
                background_music = background_music.fx(volumex, 0.3)

                # Combine voice audio with background music
                combined_audio = CompositeAudioClip([audio, background_music])
                final_video = final_video.set_audio(combined_audio)

            # Write the final video
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # Clean up
            video.close()
            audio.close()
            if background_music_path and os.path.exists(background_music_path):
                background_music.close()
            final_video.close()

            return True

        except Exception as e:
            logger.error(f"Error combining audio and video with MoviePy: {e}")
            return False

    def _combine_with_ffmpeg(self, video_path: str, audio_path: str, output_path: str,
                           background_music_path: Optional[str] = None) -> bool:
        """Combine audio with video using FFmpeg directly"""
        try:
            # Get audio duration first
            audio_info_cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', audio_path
            ]

            result = subprocess.run(audio_info_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("Failed to get audio duration")
                return False

            import json
            audio_info = json.loads(result.stdout)
            audio_duration = float(audio_info['format']['duration'])

            # Basic video + audio combination
            if background_music_path and os.path.exists(background_music_path):
                # Complex command with background music
                cmd = [
                    'ffmpeg', '-y',
                    '-stream_loop', '-1', '-i', video_path,  # Loop video
                    '-i', audio_path,  # Main audio
                    '-stream_loop', '-1', '-i', background_music_path,  # Background music
                    '-filter_complex',
                    f'[0:v]trim=duration={audio_duration}[v];'
                    f'[2:a]volume=0.3[bg];'
                    f'[1:a][bg]amix=inputs=2:duration=first[a]',
                    '-map', '[v]', '-map', '[a]',
                    '-c:v', 'libx264', '-c:a', 'aac',
                    '-shortest',
                    output_path
                ]
            else:
                # Simple command without background music
                cmd = [
                    'ffmpeg', '-y',
                    '-stream_loop', '-1', '-i', video_path,  # Loop video
                    '-i', audio_path,  # Main audio
                    '-filter_complex',
                    f'[0:v]trim=duration={audio_duration}[v]',
                    '-map', '[v]', '-map', '1:a',
                    '-c:v', 'libx264', '-c:a', 'aac',
                    '-shortest',
                    output_path
                ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info("Successfully combined audio and video with FFmpeg")
                return True
            else:
                logger.error(f"FFmpeg error: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error combining audio and video with FFmpeg: {e}")
            return False

    def generate_srt_from_script(self, script_segments: List[Tuple[int, str]], audio_duration: float) -> str:
        """Generate SRT subtitle content from script segments"""
        try:
            if not script_segments:
                return ""

            srt_content = ""
            segment_count = len(script_segments)

            # Calculate timing for each segment
            time_per_segment = audio_duration / segment_count

            for i, (speaker_num, dialogue) in enumerate(script_segments):
                start_time = i * time_per_segment
                end_time = (i + 1) * time_per_segment

                # Format time for SRT (HH:MM:SS,mmm)
                start_srt = self._seconds_to_srt_time(start_time)
                end_srt = self._seconds_to_srt_time(end_time)

                # Clean dialogue text
                clean_dialogue = dialogue.strip()
                if len(clean_dialogue) > 80:  # Split long lines
                    words = clean_dialogue.split()
                    mid_point = len(words) // 2
                    line1 = " ".join(words[:mid_point])
                    line2 = " ".join(words[mid_point:])
                    subtitle_text = f"{line1}\n{line2}"
                else:
                    subtitle_text = clean_dialogue

                # Don't add speaker labels - just show the dialogue
                srt_content += f"{i + 1}\n"
                srt_content += f"{start_srt} --> {end_srt}\n"
                srt_content += f"{subtitle_text}\n\n"

            return srt_content

        except Exception as e:
            logger.error(f"Error generating SRT: {e}")
            return ""

    def _seconds_to_srt_time(self, seconds: float) -> str:
        """Convert seconds to SRT time format (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    def add_subtitles_from_script(self, video_path: str, script_segments: List[Tuple[int, str]],
                                audio_duration: float, output_path: str,
                                subtitle_format: str = 'script', subtitle_style: str = 'white_black_outline') -> bool:
        """Add subtitles to video from script segments with customizable format and style"""
        try:
            # Handle different subtitle formats
            if subtitle_format == 'auto':
                # Try Replicate autocaption first
                logger.info("Attempting Replicate autocaption...")
                if self.add_captions_with_replicate(video_path, output_path):
                    return True
                else:
                    logger.warning("Replicate autocaption failed, falling back to script-based")
                    subtitle_format = 'script'  # Fallback to script-based

            # Generate SRT content for script-based formats
            srt_content = self.generate_srt_from_script(script_segments, audio_duration)
            if not srt_content:
                logger.warning("No subtitle content generated")
                return False

            # Create temporary subtitle file with proper path escaping
            subtitle_file = os.path.join(self.temp_dir, f"subtitles_{os.getpid()}.srt")

            with open(subtitle_file, 'w', encoding='utf-8') as f:
                f.write(srt_content)

            logger.info(f"Created subtitle file: {subtitle_file}")
            logger.info(f"Subtitle content preview:\n{srt_content[:200]}...")

            # Get style configuration based on user selection
            style_config = self.get_subtitle_style_config(subtitle_style, subtitle_format)

            # Use a simpler, more reliable subtitle approach
            # Escape the subtitle file path for Windows/Unix compatibility
            escaped_subtitle_file = subtitle_file.replace('\\', '\\\\').replace(':', '\\:')

            # Create subtitle filter with custom styling
            subtitle_filter = f"subtitles='{escaped_subtitle_file}':force_style='{style_config}'"

            # Use FFmpeg to add subtitles with better error handling
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-vf', subtitle_filter,
                '-c:a', 'copy',
                '-c:v', 'libx264',
                '-preset', 'fast',
                output_path
            ]

            logger.info(f"Running FFmpeg command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)

            # Clean up temporary subtitle file
            if os.path.exists(subtitle_file):
                os.remove(subtitle_file)

            if result.returncode == 0:
                logger.info("Successfully added subtitles to video")
                return True
            else:
                logger.error(f"FFmpeg error adding subtitles: {result.stderr}")
                logger.error(f"FFmpeg stdout: {result.stdout}")
                # Try fallback method if subtitle filter fails
                return self._add_subtitles_fallback(video_path, script_segments, audio_duration, output_path)

        except Exception as e:
            logger.error(f"Error adding subtitles from script: {e}")
            return False

    def get_subtitle_style_config(self, subtitle_style: str, subtitle_format: str) -> str:
        """Get FFmpeg subtitle style configuration based on user selection"""

        # Base font size based on format
        if subtitle_format == 'large_text':
            font_size = 36
        elif subtitle_format == 'minimal':
            font_size = 24
        else:  # script or default
            font_size = 28

        # Style configurations
        styles = {
            'white_black_outline': {
                'FontName': 'Arial',
                'FontSize': font_size,
                'PrimaryColour': '&H00FFFFFF',  # White
                'OutlineColour': '&H00000000',  # Black
                'Outline': '3',
                'Alignment': '2',  # Bottom center
                'Bold': '1',
                'MarginV': '30'
            },
            'black_white_outline': {
                'FontName': 'Arial',
                'FontSize': font_size,
                'PrimaryColour': '&H00000000',  # Black
                'OutlineColour': '&H00FFFFFF',  # White
                'Outline': '3',
                'Alignment': '2',
                'Bold': '1',
                'MarginV': '30'
            },
            'yellow_black_outline': {
                'FontName': 'Arial',
                'FontSize': font_size,
                'PrimaryColour': '&H0000FFFF',  # Yellow
                'OutlineColour': '&H00000000',  # Black
                'Outline': '3',
                'Alignment': '2',
                'Bold': '1',
                'MarginV': '30'
            },
            'white_shadow': {
                'FontName': 'Arial',
                'FontSize': font_size,
                'PrimaryColour': '&H00FFFFFF',  # White
                'BackColour': '&H80000000',  # Semi-transparent black
                'Shadow': '2',
                'Alignment': '2',
                'Bold': '1',
                'MarginV': '30'
            },
            'bold_white': {
                'FontName': 'Arial',
                'FontSize': font_size + 4,  # Larger for bold effect
                'PrimaryColour': '&H00FFFFFF',  # White
                'OutlineColour': '&H00000000',  # Black
                'Outline': '2',
                'Alignment': '2',
                'Bold': '1',
                'MarginV': '30'
            }
        }

        # Get style or default to white_black_outline
        style = styles.get(subtitle_style, styles['white_black_outline'])

        # Convert to FFmpeg format
        style_parts = []
        for key, value in style.items():
            style_parts.append(f"{key}={value}")

        return ','.join(style_parts)

    def upload_file_to_replicate(self, file_path: str, replicate_api_token: str) -> str:
        """Upload a file to Replicate and return the URL"""
        try:
            headers = {
                'Authorization': f'Token {replicate_api_token}',
            }

            # Get upload URL
            response = requests.post(
                'https://api.replicate.com/v1/files',
                headers=headers,
                json={
                    "type": "video/mp4",
                    "name": os.path.basename(file_path)
                }
            )

            if response.status_code == 201:
                upload_data = response.json()
                upload_url = upload_data['upload_url']
                file_url = upload_data['urls']['get']

                # Upload the file
                with open(file_path, 'rb') as f:
                    upload_response = requests.put(upload_url, data=f)

                if upload_response.status_code == 200:
                    logger.info(f"Successfully uploaded file to Replicate: {file_url}")
                    return file_url
                else:
                    logger.error(f"Failed to upload file: {upload_response.status_code}")
                    return None
            else:
                logger.error(f"Failed to get upload URL: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error uploading file to Replicate: {e}")
            return None

    def add_captions_with_replicate(self, video_path: str, output_path: str,
                                  replicate_api_token: str = None) -> bool:
        """Add captions using Replicate's autocaption API"""
        try:
            # Use environment variable if token not provided
            if not replicate_api_token:
                replicate_api_token = os.getenv('REPLICATE_API_TOKEN', 'b6923a2561470def58d509cc343534fc4d30aee8')

            if not replicate_api_token:
                logger.error("Replicate API token not found")
                return False

            logger.info("Adding captions using Replicate autocaption API...")

            # Upload video file to Replicate
            video_url = self.upload_file_to_replicate(video_path, replicate_api_token)
            if not video_url:
                logger.error("Failed to upload video to Replicate")
                return False

            # Prepare the API request
            headers = {
                'Authorization': f'Token {replicate_api_token}',
                'Content-Type': 'application/json'
            }

            # Replicate API payload with optimized settings
            payload = {
                "version": "18a45ff0d95feb4449d192bbdc06b4a6df168fa33def76dfc51b78ae224b599b",
                "input": {
                    "font": "Poppins/Poppins-ExtraBold.ttf",
                    "color": "white",
                    "kerning": -5,
                    "opacity": 0,
                    "MaxChars": 25,  # Increased for better readability
                    "fontsize": 8,   # Slightly larger font
                    "translate": False,
                    "output_video": True,
                    "stroke_color": "black",
                    "stroke_width": 3.0,  # Stronger outline
                    "right_to_left": False,
                    "subs_position": "bottom75",
                    "highlight_color": "yellow",
                    "video_file_input": video_url,
                    "output_transcript": True
                }
            }

            # Make the API request
            response = requests.post(
                'https://api.replicate.com/v1/predictions',
                headers=headers,
                json=payload,
                timeout=300
            )

            if response.status_code == 201:
                prediction = response.json()
                prediction_id = prediction['id']

                logger.info(f"Replicate prediction started: {prediction_id}")

                # Poll for completion
                max_wait_time = 900  # 15 minutes for longer videos
                start_time = time.time()

                while time.time() - start_time < max_wait_time:
                    status_response = requests.get(
                        f'https://api.replicate.com/v1/predictions/{prediction_id}',
                        headers=headers
                    )

                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        status = status_data.get('status')

                        logger.info(f"Replicate status: {status}")

                        if status == 'succeeded':
                            output_data = status_data.get('output')
                            if output_data and isinstance(output_data, dict):
                                video_url = output_data.get('video')
                                if video_url:
                                    # Download the captioned video
                                    logger.info(f"Downloading captioned video from: {video_url}")
                                    video_response = requests.get(video_url, timeout=300)
                                    if video_response.status_code == 200:
                                        with open(output_path, 'wb') as f:
                                            f.write(video_response.content)
                                        logger.info("Successfully downloaded captioned video from Replicate")
                                        return True
                                    else:
                                        logger.error(f"Failed to download video: {video_response.status_code}")
                            break
                        elif status == 'failed':
                            error_msg = status_data.get('error', 'Unknown error')
                            logger.error(f"Replicate prediction failed: {error_msg}")
                            break
                        elif status in ['starting', 'processing']:
                            # Continue waiting
                            pass

                        # Wait before polling again
                        time.sleep(15)
                    else:
                        logger.error(f"Error checking prediction status: {status_response.status_code}")
                        break

                logger.error("Replicate prediction timed out or failed")
                return False
            else:
                logger.error(f"Failed to start Replicate prediction: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error adding captions with Replicate: {e}")
            return False

    def _add_subtitles_fallback(self, video_path: str, script_segments: List[Tuple[int, str]],
                              audio_duration: float, output_path: str) -> bool:
        """Fallback method to add subtitles using drawtext filter"""
        try:
            logger.info("Trying fallback subtitle method using drawtext")

            # Create drawtext filters for each subtitle segment
            drawtext_filters = []
            segment_count = len(script_segments)
            time_per_segment = audio_duration / segment_count

            for i, (speaker_num, dialogue) in enumerate(script_segments):
                start_time = i * time_per_segment
                end_time = (i + 1) * time_per_segment

                # Clean and escape text for drawtext (no speaker labels)
                clean_text = dialogue.strip().replace("'", "\\'").replace(":", "\\:")
                full_text = clean_text

                # Create drawtext filter for this segment
                drawtext_filter = (
                    f"drawtext=text='{full_text}':fontsize=28:fontcolor=white:"
                    f"borderw=3:bordercolor=black:x=(w-text_w)/2:y=h-text_h-30:"
                    f"enable='between(t,{start_time},{end_time})'"
                )
                drawtext_filters.append(drawtext_filter)

            # Combine all drawtext filters
            if drawtext_filters:
                video_filter = ','.join(drawtext_filters)

                cmd = [
                    'ffmpeg', '-y',
                    '-i', video_path,
                    '-vf', video_filter,
                    '-c:a', 'copy',
                    '-c:v', 'libx264',
                    '-preset', 'fast',
                    output_path
                ]

                logger.info(f"Running fallback FFmpeg command: {' '.join(cmd[:10])}...")
                result = subprocess.run(cmd, capture_output=True, text=True)

                if result.returncode == 0:
                    logger.info("Successfully added subtitles using fallback method")
                    return True
                else:
                    logger.error(f"Fallback method also failed: {result.stderr}")
                    return False
            else:
                logger.error("No subtitle filters created")
                return False

        except Exception as e:
            logger.error(f"Error in fallback subtitle method: {e}")
            return False

    def _color_to_hex(self, color_name: str) -> str:
        """Convert color name to hex for FFmpeg"""
        color_map = {
            'white': 'FFFFFF',
            'black': '000000',
            'red': 'FF0000',
            'blue': '0000FF',
            'green': '00FF00',
            'yellow': 'FFFF00'
        }
        return color_map.get(color_name.lower(), 'FFFFFF')

    def add_subtitles(self, video_path: str, subtitle_text: str, output_path: str) -> bool:
        """Add simple subtitles to video (legacy method)"""
        try:
            # Create a temporary subtitle file
            subtitle_file = os.path.join(self.temp_dir, f"temp_subtitles_{os.getpid()}.srt")

            # Generate simple SRT content
            srt_content = f"""1
00:00:00,000 --> 00:00:10,000
{subtitle_text[:100]}...

2
00:00:10,000 --> 00:00:20,000
{subtitle_text[100:200] if len(subtitle_text) > 100 else ''}...
"""

            with open(subtitle_file, 'w', encoding='utf-8') as f:
                f.write(srt_content)

            # Use FFmpeg to add subtitles
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-vf', f"subtitles={subtitle_file}",
                '-c:a', 'copy',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            # Clean up temporary subtitle file
            if os.path.exists(subtitle_file):
                os.remove(subtitle_file)

            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error adding subtitles: {e}")
            return False

    def create_simple_background_video(self, duration: float, output_path: str,
                                     style: str = "gradient") -> bool:
        """Create a simple background video when no stock video is available"""
        try:
            if style == "gradient":
                # Create a gradient background using FFmpeg
                cmd = [
                    'ffmpeg',
                    '-f', 'lavfi',
                    '-i', f'color=c=0x1e3a8a:size=1920x1080:duration={duration}',
                    '-vf', 'fade=in:0:30,fade=out:st={duration-1}:d=1'.format(duration=duration),
                    '-c:v', 'libx264',
                    '-pix_fmt', 'yuv420p',
                    '-y',
                    output_path
                ]
            elif style == "particles":
                # Create a particle effect background
                cmd = [
                    'ffmpeg',
                    '-f', 'lavfi',
                    '-i', f'color=c=black:size=1920x1080:duration={duration}',
                    '-vf', f'geq=random(1)*255:128:128,scale=1920:1080',
                    '-c:v', 'libx264',
                    '-pix_fmt', 'yuv420p',
                    '-y',
                    output_path
                ]
            else:
                # Default solid color
                cmd = [
                    'ffmpeg',
                    '-f', 'lavfi',
                    '-i', f'color=c=darkblue:size=1920x1080:duration={duration}',
                    '-c:v', 'libx264',
                    '-pix_fmt', 'yuv420p',
                    '-y',
                    output_path
                ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error creating background: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error creating background video: {e}")
            return False

    def create_video_from_image(self, image_path: str, output_path: str, duration: float) -> bool:
        """Create a video from a static image with specified duration"""
        try:
            # Use FFmpeg to create video from image
            cmd = [
                'ffmpeg', '-y',  # -y to overwrite output file
                '-loop', '1',  # Loop the image
                '-i', image_path,  # Input image
                '-c:v', 'libx264',  # Video codec
                '-t', str(duration),  # Duration
                '-pix_fmt', 'yuv420p',  # Pixel format for compatibility
                '-vf', 'scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2',  # Scale and pad to 1080p
                '-r', '30',  # Frame rate
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info(f"Successfully created video from image: {output_path}")
                return True
            else:
                logger.error(f"FFmpeg error creating video from image: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error creating video from image: {e}")
            return False

    def get_video_info(self, video_path: str) -> dict:
        """Get video information using FFprobe"""
        try:
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                import json
                info = json.loads(result.stdout)

                video_stream = None
                for stream in info.get('streams', []):
                    if stream.get('codec_type') == 'video':
                        video_stream = stream
                        break

                if video_stream:
                    return {
                        'duration': float(info['format']['duration']),
                        'width': video_stream['width'],
                        'height': video_stream['height'],
                        'fps': eval(video_stream.get('r_frame_rate', '30/1'))
                    }

            return {}

        except Exception as e:
            logger.error(f"Error getting video info: {e}")
            return {}

    def resize_video(self, input_path: str, output_path: str, width: int = 1920, height: int = 1080) -> bool:
        """Resize video to specified dimensions"""
        try:
            cmd = [
                'ffmpeg',
                '-i', input_path,
                '-vf', f'scale={width}:{height}:force_original_aspect_ratio=decrease,pad={width}:{height}:(ow-iw)/2:(oh-ih)/2',
                '-c:a', 'copy',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error resizing video: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error resizing video: {e}")
            return False

class AudioProcessor:
    """Handles audio processing and enhancement"""

    def __init__(self):
        pass

    def normalize_audio(self, input_path: str, output_path: str) -> bool:
        """Normalize audio levels"""
        try:
            cmd = [
                'ffmpeg',
                '-i', input_path,
                '-af', 'loudnorm',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error normalizing audio: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error normalizing audio: {e}")
            return False

    def add_fade_effects(self, input_path: str, output_path: str, fade_in: float = 1.0, fade_out: float = 1.0) -> bool:
        """Add fade in/out effects to audio"""
        try:
            cmd = [
                'ffmpeg',
                '-i', input_path,
                '-af', f'afade=in:st=0:d={fade_in},afade=out:st=end-{fade_out}:d={fade_out}',
                '-y',
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return True
            else:
                logger.error(f"FFmpeg error adding fade effects: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error adding fade effects: {e}")
            return False
