#!/usr/bin/env python3
"""
Test Google Text-to-Speech API integration
"""

import requests
import base64
import os
import json

def test_google_tts():
    """Test Google TTS API with the provided key"""
    
    # API key from your example
    api_key = "AIzaSyBri7pBtSmy-rbrSoM2UdztBMkpqdjbztk"
    
    # Google TTS API URL
    url = f"https://texttospeech.googleapis.com/v1/text:synthesize?key={api_key}"
    
    # Test payload
    payload = {
        "input": {
            "text": "Hello! This is a test of Google Text-to-Speech API. Speaker 1 speaking."
        },
        "voice": {
            "languageCode": "en-US",
            "name": "en-US-Wavenet-A"
        },
        "audioConfig": {
            "audioEncoding": "MP3",
            "speakingRate": 1.0,
            "pitch": 0.0,
            "volumeGainDb": 0.0
        }
    }
    
    print("🎙️ Testing Google TTS API...")
    print(f"API Key: {api_key[:20]}...")
    print(f"URL: {url}")
    
    try:
        # Make API request
        response = requests.post(url, json=payload)
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            response_json = response.json()
            audio_content = response_json.get('audioContent')
            
            if audio_content:
                # Save test audio file
                output_file = "test_google_tts.mp3"
                with open(output_file, 'wb') as f:
                    f.write(base64.b64decode(audio_content))
                
                print(f"✅ SUCCESS! Audio saved to: {output_file}")
                print(f"File size: {os.path.getsize(output_file)} bytes")
                return True
            else:
                print("❌ ERROR: No audio content in response")
                print(f"Response: {response_json}")
                return False
        else:
            print(f"❌ ERROR: API request failed")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

def test_voice_list():
    """Test getting available voices"""
    
    api_key = "AIzaSyBri7pBtSmy-rbrSoM2UdztBMkpqdjbztk"
    url = f"https://texttospeech.googleapis.com/v1/voices?key={api_key}"
    
    print("\n🎭 Testing Voice List API...")
    
    try:
        response = requests.get(url)
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            voices_data = response.json()
            voices = voices_data.get('voices', [])
            
            print(f"✅ SUCCESS! Found {len(voices)} voices")
            
            # Show English voices
            en_voices = [v for v in voices if 'en-US' in v.get('languageCodes', [])]
            print(f"\n🇺🇸 English (US) voices: {len(en_voices)}")
            
            for voice in en_voices[:5]:  # Show first 5
                name = voice.get('name', 'Unknown')
                gender = voice.get('ssmlGender', 'Unknown')
                print(f"  - {name} ({gender})")
            
            return True
        else:
            print(f"❌ ERROR: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

def test_multi_speaker():
    """Test multi-speaker conversation"""
    
    api_key = "AIzaSyBri7pBtSmy-rbrSoM2UdztBMkpqdjbztk"
    url = f"https://texttospeech.googleapis.com/v1/text:synthesize?key={api_key}"
    
    print("\n🎭 Testing Multi-Speaker Conversation...")
    
    # Test script
    speakers = [
        {
            "text": "Welcome to our podcast! I'm your host James.",
            "voice": "en-US-Wavenet-A",  # Male
            "name": "James"
        },
        {
            "text": "Thanks for having me, James. I'm excited to be here!",
            "voice": "en-US-Wavenet-C",  # Female
            "name": "Linda"
        },
        {
            "text": "Let's talk about artificial intelligence and its impact on society.",
            "voice": "en-US-Wavenet-A",  # Male
            "name": "James"
        },
        {
            "text": "AI is transforming every industry. The possibilities are endless!",
            "voice": "en-US-Wavenet-C",  # Female
            "name": "Linda"
        }
    ]
    
    audio_segments = []
    
    for i, speaker in enumerate(speakers):
        payload = {
            "input": {"text": speaker["text"]},
            "voice": {
                "languageCode": "en-US",
                "name": speaker["voice"]
            },
            "audioConfig": {
                "audioEncoding": "MP3",
                "speakingRate": 1.0,
                "pitch": 0.0,
                "volumeGainDb": 0.0
            }
        }
        
        print(f"  Generating: {speaker['name']} - {speaker['text'][:30]}...")
        
        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                response_json = response.json()
                audio_content = response_json.get('audioContent')
                if audio_content:
                    audio_segments.append(base64.b64decode(audio_content))
                    print(f"    ✅ Generated {len(audio_segments[-1])} bytes")
                else:
                    print(f"    ❌ No audio content")
                    return False
            else:
                print(f"    ❌ API Error: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"    ❌ Exception: {e}")
            return False
    
    # Combine audio segments (simple concatenation)
    if audio_segments:
        combined_audio = b''.join(audio_segments)
        output_file = "test_multi_speaker.mp3"
        
        with open(output_file, 'wb') as f:
            f.write(combined_audio)
        
        print(f"✅ SUCCESS! Multi-speaker audio saved to: {output_file}")
        print(f"Combined file size: {len(combined_audio)} bytes")
        return True
    
    return False

if __name__ == "__main__":
    print("🎙️ Google TTS API Test Suite")
    print("=" * 50)
    
    # Test 1: Basic TTS
    test1 = test_google_tts()
    
    # Test 2: Voice list
    test2 = test_voice_list()
    
    # Test 3: Multi-speaker
    test3 = test_multi_speaker()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Basic TTS: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"  Voice List: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"  Multi-Speaker: {'✅ PASS' if test3 else '❌ FAIL'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 ALL TESTS PASSED! Google TTS is working correctly!")
    else:
        print("\n⚠️  Some tests failed. Check API key and network connection.")
