import openai
import requests
import os
from dotenv import load_dotenv
import json
import re
from typing import List, Dict, Tuple

load_dotenv()

# Dynamic API key loading from database
def get_api_key(service):
    """Get API key from database settings or fallback to environment"""
    try:
        from app import get_setting
        db_key = get_setting(f'{service}_api_key')
        if db_key:
            return db_key
    except:
        pass

    # Fallback to environment variables
    env_keys = {
        'openai': 'OPENAI_API_KEY',
        'elevenlabs': 'ELEVENLABS_API_KEY',
        'pexels': 'PEXELS_API_KEY'
    }
    return os.getenv(env_keys.get(service, ''))

def get_llm_provider():
    """Get current LLM provider from settings"""
    try:
        from app import get_setting
        return get_setting('llm_provider', 'openai')
    except:
        return 'openai'

def get_tts_provider():
    """Get current TTS provider from settings"""
    try:
        from app import get_setting
        return get_setting('tts_provider', 'elevenlabs')
    except:
        return 'elevenlabs'

def get_video_provider():
    """Get current video provider from settings"""
    try:
        from app import get_setting
        return get_setting('video_provider', 'pexels')
    except:
        return 'pexels'

# Initialize clients dynamically
def get_openai_client():
    """Get OpenAI client with current API key"""
    api_key = get_api_key('openai')
    if api_key:
        return openai.OpenAI(api_key=api_key)
    return None

def get_elevenlabs_client():
    """Get ElevenLabs client with current API key"""
    try:
        from elevenlabs.client import ElevenLabs
        api_key = get_api_key('elevenlabs')
        if api_key:
            return ElevenLabs(api_key=api_key)
    except ImportError:
        pass
    return None

class ContentGenerator:
    """Handles AI-powered content generation for podcasts and postcards"""

    def __init__(self):
        self.openai_client = None

    def get_client(self):
        """Get the appropriate LLM client based on settings"""
        if not self.openai_client:
            self.openai_client = get_openai_client()
        return self.openai_client

    def generate_podcast_script(self, keyword: str, language: str, num_speakers: int, tone: str) -> str:
        """Generate a podcast script based on the given parameters"""

        language_prompts = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'zh': 'Chinese'
        }

        lang_name = language_prompts.get(language, 'English')

        if num_speakers == 1:
            prompt = f"""
            Generate a {tone} podcast script in {lang_name} about '{keyword}'.
            The script should be for 1 speaker and last approximately 2-3 minutes when spoken.

            Format the script as:
            Speaker 1: [dialogue]

            Make it engaging, informative, and natural-sounding. Include natural pauses and transitions.
            The tone should be {tone}.
            """
        else:
            prompt = f"""
            Generate a {tone} podcast script in {lang_name} about '{keyword}' for {num_speakers} speakers.
            The script should last approximately 2-3 minutes when spoken and include natural conversation between the speakers.

            Format the script as:
            Speaker 1: [dialogue]
            Speaker 2: [dialogue]
            {f'Speaker 3: [dialogue]' if num_speakers == 3 else ''}

            Ensure natural turn-taking between speakers, with each speaker having a distinct perspective or role.
            Make the conversation engaging, informative, and natural-sounding.
            The tone should be {tone}.
            """

        try:
            client = self.get_client()
            if not client:
                return "Error: No LLM client available. Please configure API keys in admin settings."

            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional podcast script writer who creates engaging, natural-sounding dialogue."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.7
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            print(f"Error generating podcast script: {e}")
            return f"Error generating script: {str(e)}"

    def generate_postcard_text(self, keyword: str, language: str, tone: str) -> str:
        """Generate postcard text based on the given parameters"""

        language_prompts = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'zh': 'Chinese'
        }

        lang_name = language_prompts.get(language, 'English')

        prompt = f"""
        Write a short, {tone} postcard message in {lang_name} about '{keyword}'.
        The message should be 2-4 sentences long, as if writing to a friend.
        Make it personal, engaging, and capture the essence of '{keyword}'.
        The tone should be {tone}.
        """

        try:
            client = self.get_client()
            if not client:
                return "Check out this amazing podcast! Don't miss this engaging discussion."

            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a creative writer who crafts engaging, personal postcard messages."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.8
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            print(f"Error generating postcard text: {e}")
            return f"Error generating postcard: {str(e)}"

    def generate_postcard_from_content(self, content_preview: str, language: str, tone: str) -> str:
        """Generate postcard text based on existing content"""

        language_prompts = {
            'en': 'English',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'zh': 'Chinese'
        }

        lang_name = language_prompts.get(language, 'English')

        prompt = f"""
        Based on this podcast content: "{content_preview}"

        Write a short, {tone} postcard message in {lang_name} that captures the essence of this content.
        The message should be 2-4 sentences long, as if writing to a friend about this interesting podcast.
        Make it engaging and encourage them to listen.
        The tone should be {tone}.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a creative writer who crafts engaging, personal postcard messages about podcast content."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.8
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            print(f"Error generating postcard from content: {e}")
            return f"Check out this amazing podcast content! A {tone} discussion that you won't want to miss."

class VoiceGenerator:
    """Handles text-to-speech generation with multiple voices"""

    def __init__(self):
        self.voice_mapping = {
            1: "Rachel",  # Female voice
            2: "Josh",    # Male voice
            3: "Bella"    # Another female voice
        }

    def parse_script(self, script: str) -> List[Tuple[int, str]]:
        """Parse script into speaker segments"""
        segments = []
        lines = script.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Match "Speaker X: dialogue" format
            match = re.match(r'Speaker (\d+):\s*(.*)', line)
            if match:
                speaker_num = int(match.group(1))
                dialogue = match.group(2).strip()
                if dialogue:
                    segments.append((speaker_num, dialogue))

        return segments

    def generate_audio_from_script(self, script: str, output_path: str) -> bool:
        """Generate audio from script with multiple voices"""
        try:
            client = get_elevenlabs_client()
            if not client:
                print("ElevenLabs client not available")
                return False

            segments = self.parse_script(script)
            if not segments:
                print("No valid segments found in script")
                return False

            audio_segments = []

            for speaker_num, dialogue in segments:
                voice_name = self.voice_mapping.get(speaker_num, "Rachel")

                # Generate audio for this segment using new API
                try:
                    audio = client.generate(
                        text=dialogue,
                        voice=voice_name,
                        model="eleven_monolingual_v1"
                    )

                    # Convert generator to bytes if needed
                    if hasattr(audio, '__iter__'):
                        audio = b''.join(audio)

                    audio_segments.append(audio)

                    # Add a small pause between speakers
                    if len(audio_segments) > 1:
                        # Add 0.5 second pause (silence)
                        pause = b'\x00' * int(22050 * 0.5 * 2)  # 22050 Hz, 0.5 sec, 16-bit
                        audio_segments.append(pause)

                except Exception as voice_error:
                    print(f"Error generating voice for speaker {speaker_num}: {voice_error}")
                    # Create a simple text-to-speech fallback or skip
                    continue

            if not audio_segments:
                print("No audio segments generated")
                return False

            # Combine all audio segments
            combined_audio = b''.join(audio_segments)

            # Save to file
            with open(output_path, 'wb') as f:
                f.write(combined_audio)

            return True

        except Exception as e:
            print(f"Error generating audio: {e}")
            return False

    def generate_simple_audio(self, text: str, output_path: str, voice_name: str = "Rachel") -> bool:
        """Generate simple audio from text (for postcards)"""
        try:
            client = get_elevenlabs_client()
            if not client:
                print("ElevenLabs client not available")
                return False

            audio = client.generate(
                text=text,
                voice=voice_name,
                model="eleven_monolingual_v1"
            )

            # Convert generator to bytes if needed
            if hasattr(audio, '__iter__'):
                audio = b''.join(audio)

            with open(output_path, 'wb') as f:
                f.write(audio)

            return True

        except Exception as e:
            print(f"Error generating simple audio: {e}")
            return False

class VideoService:
    """Handles video search and retrieval from stock video APIs"""

    def __init__(self):
        self.pexels_api_key = None
        self.base_url = "https://api.pexels.com/videos/search"

    def get_api_key(self):
        """Get current video provider API key"""
        if not self.pexels_api_key:
            self.pexels_api_key = get_api_key('pexels')
        return self.pexels_api_key

    def search_videos(self, keyword: str, per_page: int = 10) -> List[Dict]:
        """Search for videos on Pexels"""
        api_key = self.get_api_key()
        if not api_key:
            return []

        headers = {
            'Authorization': api_key
        }

        params = {
            'query': keyword,
            'per_page': per_page,
            'orientation': 'landscape'
        }

        try:
            response = requests.get(self.base_url, headers=headers, params=params)
            response.raise_for_status()

            data = response.json()
            videos = []

            for video in data.get('videos', []):
                video_info = {
                    'id': video['id'],
                    'url': video['url'],
                    'duration': video['duration'],
                    'width': video['width'],
                    'height': video['height'],
                    'preview': video['image'],
                    'download_url': None
                }

                # Get the best quality video file
                video_files = video.get('video_files', [])
                if video_files:
                    # Sort by quality (width) and get the best one
                    best_video = max(video_files, key=lambda x: x.get('width', 0))
                    video_info['download_url'] = best_video['link']

                videos.append(video_info)

            return videos

        except Exception as e:
            print(f"Error searching videos: {e}")
            return []

    def download_video(self, video_url: str, output_path: str) -> bool:
        """Download video from URL"""
        try:
            response = requests.get(video_url, stream=True)
            response.raise_for_status()

            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            return True

        except Exception as e:
            print(f"Error downloading video: {e}")
            return False
