# 🎙️ Google Text-to-Speech Voice Selection - Complete Guide

## 🎯 New Feature: Advanced Voice Selection with Google TTS

The AI Podcast Generator now supports **Google Text-to-Speech API** with comprehensive voice selection capabilities. Choose from dozens of high-quality neural voices across 9 languages for professional podcast creation.

---

## 🚀 Key Features

### **🌍 Multi-Language Voice Support**
- **9 Languages**: English, Spanish, French, German, Italian, Portuguese, Japanese, Korean, Chinese
- **Neural Voices**: High-quality WaveNet voices for natural speech
- **Gender Options**: Male and female voices for each language
- **Regional Variants**: US/UK English, Spain/Latin Spanish, etc.

### **🎭 Multi-Speaker Conversations**
- **Individual Voice Selection**: Choose different voices for each speaker
- **Smart Defaults**: Automatic voice assignment based on gender variety
- **Real-time Preview**: See voice selections before generating
- **Dynamic Interface**: Voice options update based on language selection

### **⚙️ Admin Configuration**
- **API Key Management**: Secure Google TTS API key storage
- **Provider Selection**: Choose between ElevenLabs and Google TTS
- **Real-time Testing**: Validate API keys before saving
- **Fallback Support**: Graceful degradation if service unavailable

---

## 🎵 Available Voices by Language

### **🇺🇸 English (US)**
- **<PERSON>** (Male) - en-US-Wavenet-A
- **<PERSON>** (Male) - en-US-Wavenet-B  
- **Linda** (Female) - en-US-Wavenet-C
- **Michael** (Male) - en-US-Wavenet-D
- **Emma** (Female) - en-US-Wavenet-E
- **Brian** (Male) - en-US-Wavenet-F

### **🇪🇸 Spanish**
- **Sofia** (Female) - es-ES-Wavenet-A (Spain)
- **Carlos** (Male) - es-ES-Wavenet-B (Spain)
- **Isabella** (Female) - es-US-Wavenet-A (US)
- **Diego** (Male) - es-US-Wavenet-B (US)

### **🇫🇷 French**
- **Adele** (Female) - fr-FR-Wavenet-A
- **Raphael** (Male) - fr-FR-Wavenet-B
- **Lina** (Female) - fr-FR-Wavenet-C
- **Victor** (Male) - fr-FR-Wavenet-D

### **🇩🇪 German**
- **Anna** (Female) - de-DE-Wavenet-A
- **Klaus** (Male) - de-DE-Wavenet-B
- **Petra** (Female) - de-DE-Wavenet-C
- **Hans** (Male) - de-DE-Wavenet-D

### **🇮🇹 Italian**
- **Giulia** (Female) - it-IT-Wavenet-A
- **Marco** (Male) - it-IT-Wavenet-B
- **Francesca** (Female) - it-IT-Wavenet-C
- **Alessandro** (Male) - it-IT-Wavenet-D

### **🇧🇷 Portuguese**
- **Camila** (Female) - pt-BR-Wavenet-A
- **Ricardo** (Male) - pt-BR-Wavenet-B

### **🇯🇵 Japanese**
- **Akiko** (Female) - ja-JP-Wavenet-A
- **Hiroshi** (Male) - ja-JP-Wavenet-B
- **Yuki** (Male) - ja-JP-Wavenet-C
- **Emi** (Male) - ja-JP-Wavenet-D

### **🇰🇷 Korean**
- **Min-jung** (Female) - ko-KR-Wavenet-A
- **Seung-ho** (Female) - ko-KR-Wavenet-B
- **Jin-woo** (Male) - ko-KR-Wavenet-C
- **So-young** (Male) - ko-KR-Wavenet-D

### **🇨🇳 Chinese (Mandarin)**
- **Li Wei** (Female) - cmn-CN-Wavenet-A
- **Zhang Ming** (Male) - cmn-CN-Wavenet-B
- **Wang Mei** (Male) - cmn-CN-Wavenet-C
- **Chen Hao** (Male) - cmn-CN-Wavenet-D

---

## 🔧 How to Use Voice Selection

### **Step 1: Admin Setup**
1. **Login as Admin**: Use admin/admin123
2. **Go to Settings**: Admin Dashboard → System Settings
3. **Configure TTS**: Select "Google" as TTS Provider
4. **Add API Key**: Enter your Google TTS API key
5. **Test Connection**: Click "Test" to verify
6. **Save Settings**: Apply configuration

### **Step 2: Create Project with Voice Selection**
1. **Create Project**: Go to "Create New Project"
2. **Choose Language**: Select your target language
3. **Set Speakers**: Choose 1-3 speakers
4. **Voice Selection**: Voices auto-populate based on language
5. **Customize Voices**: Select different voices for each speaker
6. **Preview**: See voice assignments in real-time

### **Step 3: Generate Podcast**
1. **Add Content**: Enter keyword or paste script
2. **Review Settings**: Check language, speakers, and voices
3. **Generate**: Click "Create Podcast"
4. **Listen**: Download professional multi-voice audio

---

## 🎛️ Voice Selection Interface

### **Dynamic Voice Loading**
```javascript
// Voices update automatically when language changes
function updateVoiceSelections() {
    const language = document.getElementById('language').value;
    fetch(`/api/voices/${language}`)
        .then(response => response.json())
        .then(data => updateVoiceDropdowns(data.voices));
}
```

### **Smart Speaker Management**
- **Speaker 1**: Always visible (primary speaker)
- **Speaker 2**: Shows when 2+ speakers selected
- **Speaker 3**: Shows when 3 speakers selected
- **Auto-assignment**: Different voices assigned by default

### **Real-time Preview**
- **Voice Names**: Display friendly names (e.g., "James (Male)")
- **Language Info**: Show language and region
- **Gender Indicators**: Clear male/female identification
- **Live Updates**: Preview changes instantly

---

## 🔧 Technical Implementation

### **Google TTS Integration**
```python
# Google TTS API call
payload = {
    "input": {"text": dialogue.strip()},
    "voice": {
        "languageCode": voice_config['languageCode'],
        "name": voice_config['value']
    },
    "audioConfig": {
        "audioEncoding": "MP3",
        "speakingRate": 1.0,
        "pitch": 0.0,
        "volumeGainDb": 0.0
    }
}
```

### **Voice Configuration System**
```python
GOOGLE_TTS_VOICES = {
    'en': [
        {'value': 'en-US-Wavenet-A', 'name': 'James', 'gender': 'Male'},
        {'value': 'en-US-Wavenet-C', 'name': 'Linda', 'gender': 'Female'},
        # ... more voices
    ]
}
```

### **Multi-Speaker Processing**
```python
def generate_google_tts_audio(script, output_path, language, voice_selections):
    segments = parse_script(script)  # Extract speaker segments
    for speaker_num, dialogue in segments:
        voice_index = voice_selections.get(f'speaker_{speaker_num}', 0)
        voice_config = available_voices[voice_index]
        # Generate audio for this segment
```

---

## 🎯 Use Cases & Benefits

### **🎭 Professional Podcasts**
- **Interview Style**: Different voices for host and guest
- **Debate Format**: Distinct voices for opposing viewpoints
- **Narrative Style**: Multiple characters with unique voices
- **Educational**: Teacher and student conversations

### **🌍 Multilingual Content**
- **Language Learning**: Native speaker pronunciation
- **Global Audience**: Regional accent preferences
- **Cultural Authenticity**: Appropriate voice selection
- **Accessibility**: Clear, natural speech patterns

### **🎨 Creative Projects**
- **Storytelling**: Character voice differentiation
- **Drama**: Emotional range and expression
- **Comedy**: Timing and delivery variations
- **Documentary**: Professional narrator voices

---

## 🔄 Provider Comparison

### **Google TTS Advantages**
- **Voice Variety**: 50+ voices across 9 languages
- **Neural Quality**: WaveNet technology for natural speech
- **Language Support**: Extensive multilingual capabilities
- **Cost Effective**: Competitive pricing for high volume
- **Reliability**: Google Cloud infrastructure

### **ElevenLabs Advantages**
- **Voice Cloning**: Custom voice creation
- **Emotional Range**: Advanced expression control
- **Premium Quality**: Ultra-realistic speech synthesis
- **Voice Consistency**: Stable character voices
- **Creative Control**: Fine-tuned voice parameters

---

## 🚀 Getting Started

### **Quick Setup**
1. **Get API Key**: Visit [Google Cloud Console](https://console.cloud.google.com/)
2. **Enable TTS API**: Activate Text-to-Speech API
3. **Create Credentials**: Generate API key
4. **Configure System**: Add key in admin settings
5. **Test Voices**: Create sample project

### **Demo Project**
```
Language: English
Speakers: 2
Speaker 1: James (Male) - Professional host voice
Speaker 2: Linda (Female) - Expert guest voice

Script:
Speaker 1: Welcome to Tech Talk! I'm your host James.
Speaker 2: Thanks for having me, James. I'm excited to discuss AI.
Speaker 1: Let's dive into the future of artificial intelligence.
Speaker 2: The possibilities are truly endless!
```

---

## 🎯 Best Practices

### **Voice Selection Tips**
- **Gender Variety**: Mix male and female voices for conversations
- **Language Matching**: Use native speakers for each language
- **Character Consistency**: Keep same voice for recurring speakers
- **Audience Consideration**: Choose voices that match your target demographic

### **Quality Optimization**
- **Clear Scripts**: Well-formatted speaker segments
- **Natural Language**: Conversational, not formal text
- **Proper Punctuation**: Helps with speech rhythm
- **Testing**: Preview voices before final generation

---

**🎙️ Professional multi-voice podcasts with Google's advanced TTS technology! Choose your voices and create amazing content! ✨**
