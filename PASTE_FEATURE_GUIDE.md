# 📝 Paste Your Own Podcast Script - Feature Guide

## 🎯 New Feature: Custom Script Input

The AI Podcast Generator now supports **two content creation modes**:

1. **🤖 AI Generation** - Let AI create content from keywords
2. **📝 Paste Your Own** - Use your existing script or content

---

## 🚀 How to Use the Paste Feature

### **Step 1: Choose Content Type**
When creating a new project:
1. Go to **Create New Project**
2. Select **"Paste Your Own Text"** from the dropdown
3. The interface will switch to custom script mode

### **Step 2: Paste Your Script**
- **Large Text Area:** Paste your podcast script (up to 5,000 characters)
- **Real-time Counter:** See character count as you type
- **Auto-resize:** Text area grows with your content

### **Step 3: Configure Settings**
- **Language:** Choose the language for voice synthesis
- **Speakers:** Select 1-3 speakers for multi-voice support
- **Tone:** Pick the tone for postcard generation

### **Step 4: Process & Generate**
- Click **"Create Podcast"**
- Watch real-time processing
- Get professional audio and video output

---

## 🎙️ Script Formatting Guide

### **Single Speaker Format**
```
Welcome to today's podcast about artificial intelligence. 
AI is transforming our world in incredible ways. 
From healthcare to transportation, we're seeing revolutionary changes.
Let's explore what this means for our future.
```

### **Multi-Speaker Format**
```
Speaker 1: Welcome to Tech Talk! I'm your host Sarah.
Speaker 2: And I'm Mike, thanks for having me on the show.
Speaker 1: Today we're discussing the future of AI. Mike, what's your take?
Speaker 2: Well Sarah, I think we're just scratching the surface...
Speaker 1: That's fascinating! Can you tell us more about...
```

### **Three Speaker Example**
```
Speaker 1: Welcome everyone to our roundtable discussion.
Speaker 2: Great to be here! This topic is so important.
Speaker 3: Absolutely, I'm excited to share my perspective.
Speaker 1: Let's start with the basics. Speaker 2, how would you define...
```

---

## ✨ Features & Benefits

### **🎵 Professional Audio Generation**
- **Multi-Voice Support:** Different voices for each speaker
- **Natural Dialogue:** Proper pauses and intonation
- **High Quality:** Professional TTS synthesis
- **Audio Processing:** Normalization and fade effects

### **🎬 Smart Video Integration**
- **Content Analysis:** Extracts keywords from your script
- **Video Search:** Finds relevant stock footage
- **Background Options:** Fallback generated backgrounds
- **Perfect Sync:** Audio-video synchronization

### **📱 Enhanced Interface**
- **Real-time Preview:** See what you'll get
- **Character Counter:** Track script length
- **Auto-resize:** Expanding text area
- **Validation:** Smart error checking

### **🔄 Flexible Workflow**
- **Instant Processing:** No AI generation wait time
- **Edit Friendly:** Easy to modify and reprocess
- **Project Management:** Save and organize custom projects
- **Download Ready:** Professional MP4 output

---

## 🎯 Use Cases

### **📚 Educational Content**
- **Lectures:** Convert written lectures to audio-visual
- **Tutorials:** Transform how-to guides into podcasts
- **Courses:** Create engaging educational content

### **📰 News & Information**
- **Articles:** Turn blog posts into podcast episodes
- **Reports:** Convert research into audio format
- **Updates:** Create news-style podcasts

### **🎭 Creative Content**
- **Stories:** Bring written stories to life
- **Interviews:** Format existing interviews
- **Discussions:** Create debate-style content

### **💼 Business Content**
- **Presentations:** Convert slides to audio
- **Training:** Transform manuals into podcasts
- **Marketing:** Create promotional content

---

## 🔧 Technical Details

### **Processing Pipeline**
1. **Script Analysis:** Parses speaker segments
2. **Voice Assignment:** Maps speakers to different voices
3. **Audio Generation:** Creates professional TTS
4. **Keyword Extraction:** Finds relevant terms for video search
5. **Video Integration:** Combines audio with visuals
6. **Final Output:** Produces downloadable MP4

### **Supported Formats**
- **Text Length:** Up to 5,000 characters
- **Languages:** 9 supported languages
- **Speakers:** 1-3 different voices
- **Output:** High-quality MP4 video

### **Smart Features**
- **Auto-titling:** Creates titles from script content
- **Postcard Generation:** AI creates social media text
- **Progress Tracking:** Real-time status updates
- **Error Handling:** Graceful failure recovery

---

## 💡 Pro Tips

### **📝 Writing Tips**
- **Clear Speakers:** Use consistent "Speaker 1:", "Speaker 2:" format
- **Natural Flow:** Write conversational, not formal text
- **Proper Length:** 2-3 minutes of content works best
- **Engaging Content:** Hook listeners from the start

### **🎙️ Voice Tips**
- **Speaker Count:** Match your script's actual speakers
- **Language Selection:** Choose the script's primary language
- **Tone Setting:** Affects postcard generation style

### **🎬 Video Tips**
- **Keywords:** Include descriptive words for better video matching
- **Content Type:** Visual topics get better video results
- **Backup Plan:** System creates backgrounds if no videos found

---

## 🔄 Workflow Comparison

### **AI Generation Mode**
1. Enter keyword → 2. AI creates script → 3. Generate audio → 4. Select video → 5. Download

### **Paste Your Own Mode**
1. Paste script → 2. Generate audio → 3. Select video → 4. Download

**⚡ Faster workflow with your own content!**

---

## 🎉 Getting Started

### **Quick Test**
1. **Login:** Use demo credentials (demo_user/demo123)
2. **Create Project:** Click "Create New Project"
3. **Select Mode:** Choose "Paste Your Own Text"
4. **Paste Sample:**
   ```
   Speaker 1: Welcome to our test podcast!
   Speaker 2: Thanks for having me. This is exciting!
   Speaker 1: Let's see how this AI technology works.
   ```
5. **Process:** Click "Create Podcast" and watch the magic!

### **Full Demo**
- **URL:** http://localhost:5000/demo_login
- **Instant Access:** No password needed
- **Try Both Modes:** Compare AI vs. custom content

---

## 🎯 Perfect For

- **Content Creators** with existing scripts
- **Educators** converting written materials
- **Businesses** repurposing content
- **Podcasters** testing new formats
- **Writers** bringing stories to life

---

**🎙️ Ready to transform your text into professional podcasts? Start pasting! ✨**
