#!/usr/bin/env python3
"""
AI Podcast Generator - Startup Script
This script starts the Flask application with proper configuration.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the Flask app
from app import app, db, socketio

def create_directories():
    """Create necessary directories for the application"""
    directories = [
        'uploads',
        'uploads/audio',
        'uploads/video',
        'uploads/final',
        'static',
        'static/css',
        'static/js'
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def check_environment():
    """Check if required environment variables are set"""
    required_vars = [
        'OPENAI_API_KEY',
        'ELEVENLABS_API_KEY',
        'PEXELS_API_KEY',
        'SECRET_KEY'
    ]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print("⚠️  Warning: Missing environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease copy .env.example to .env and fill in your API keys.")
        print("The application will still run but some features may not work.")
    else:
        print("✓ All required environment variables are set")

def initialize_database():
    """Initialize the database"""
    with app.app_context():
        try:
            db.create_all()
            print("✓ Database initialized successfully")

            # Create admin user if it doesn't exist
            from werkzeug.security import generate_password_hash
            from app import User

            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    is_admin=True
                )
                db.session.add(admin_user)
                print("✓ Admin user created (admin/admin123)")

            # Create demo users if they don't exist
            demo_user = User.query.filter_by(username='demo_user').first()
            if not demo_user:
                demo_user = User(
                    username='demo_user',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('demo123'),
                    is_demo=True
                )
                db.session.add(demo_user)
                print("✓ Demo user created (demo_user/demo123)")

            creator_user = User.query.filter_by(username='content_creator').first()
            if not creator_user:
                creator_user = User(
                    username='content_creator',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('creator123'),
                    is_demo=True
                )
                db.session.add(creator_user)
                print("✓ Content creator user created (content_creator/creator123)")

            db.session.commit()

        except Exception as e:
            print(f"❌ Error initializing database: {e}")
            return False
    return True

def main():
    """Main startup function"""
    print("🎙️  AI Podcast Generator - Starting up...")
    print("=" * 50)

    # Create directories
    create_directories()

    # Check environment
    check_environment()

    # Initialize database
    if not initialize_database():
        sys.exit(1)

    print("\n🚀 Starting Flask application...")
    print("📱 Open your browser and go to: http://localhost:5000")
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 50)

    # Start the application
    try:
        socketio.run(
            app,
            debug=True,
            host='0.0.0.0',
            port=5000,
            allow_unsafe_werkzeug=True
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down gracefully...")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
