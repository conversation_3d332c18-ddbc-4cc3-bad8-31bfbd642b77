#!/usr/bin/env python3
"""
Test subtitle visibility in generated videos
"""

import os
import sys
import tempfile

def test_subtitle_visibility():
    """Test that subtitles are properly visible in videos"""
    print("🎬 Testing Subtitle Visibility...")
    
    try:
        from video_processor import VideoProcessor
        
        # Create a test video processor
        vp = VideoProcessor()
        
        # Test script segments
        test_segments = [
            (1, "Bonjour et bienvenue dans Triangle."),
            (2, "Merci de m'avoir invitée aujourd'hui."),
            (1, "Parlons de mystères africains."),
            (2, "C'est un sujet fascinant.")
        ]
        
        # Test SRT generation
        audio_duration = 20.0
        srt_content = vp.generate_srt_from_script(test_segments, audio_duration)
        
        print("✅ Generated SRT content:")
        print("=" * 50)
        print(srt_content)
        print("=" * 50)
        
        # Test subtitle file creation
        temp_dir = tempfile.gettempdir()
        subtitle_file = os.path.join(temp_dir, "test_subtitles.srt")
        
        with open(subtitle_file, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        
        print(f"✅ Created test subtitle file: {subtitle_file}")
        
        # Verify file exists and has content
        if os.path.exists(subtitle_file):
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📄 File size: {len(content)} characters")
                print(f"📊 Number of subtitle blocks: {content.count('-->')}")
        
        # Clean up
        if os.path.exists(subtitle_file):
            os.remove(subtitle_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing subtitle visibility: {e}")
        return False

def create_test_project_with_visible_subtitles():
    """Create a test project specifically for subtitle visibility"""
    print("\n🎯 Creating Test Project with Visible Subtitles...")
    
    try:
        from app import app, db, Project, User
        import uuid
        
        with app.app_context():
            # Get demo user
            demo_user = User.query.filter_by(username='demo').first()
            if not demo_user:
                print("❌ Demo user not found")
                return None
            
            # Create project with clear, visible subtitle content
            test_script = """Speaker 1: BONJOUR ET BIENVENUE DANS TRIANGLE.
Speaker 2: MERCI DE M'AVOIR INVITÉE AUJOURD'HUI.
Speaker 1: NOUS PARLONS DE MYSTÈRES AFRICAINS.
Speaker 2: C'EST UN SUJET TRÈS FASCINANT.
Speaker 1: PARLEZ-NOUS DE VOTRE EXPÉRIENCE.
Speaker 2: TOUT A COMMENCÉ DANS MON VILLAGE."""
            
            project = Project(
                id=str(uuid.uuid4()),
                user_id=demo_user.id,
                title="TEST SUBTITLES VISIBILITY - TRIANGLE",
                content_type='paste',
                keyword='test subtitles visibility',
                custom_script=test_script,
                script=test_script,
                language='fr',
                num_speakers=2,
                tone='conversational',
                auto_create_video=True,
                include_subtitles=True,  # ✅ Subtitles enabled!
                status='created'
            )
            
            db.session.add(project)
            db.session.commit()
            
            print(f"✅ Created subtitle visibility test project!")
            print(f"📋 Project ID: {project.id}")
            print(f"🎬 Title: {project.title}")
            print(f"📝 Subtitles enabled: {project.include_subtitles}")
            print(f"🌐 URL: http://localhost:5000/project/{project.id}")
            
            return project.id
            
    except Exception as e:
        print(f"❌ Error creating project: {e}")
        return None

def show_subtitle_troubleshooting():
    """Show troubleshooting tips for subtitle visibility"""
    print("\n" + "="*60)
    print("🔧 SUBTITLE VISIBILITY TROUBLESHOOTING")
    print("="*60)
    
    tips = [
        "🎬 IMPROVED SUBTITLE IMPLEMENTATION:",
        "",
        "✅ What's been fixed:",
        "  🎯 Larger font size (28px instead of 24px)",
        "  🔍 Better outline (3px black border)",
        "  📍 Improved positioning (30px from bottom)",
        "  🛡️  Fallback method using drawtext filter",
        "  📝 Better error logging and debugging",
        "",
        "🔧 Technical improvements:",
        "  - Primary method: SRT file with subtitles filter",
        "  - Fallback method: drawtext filter for each segment",
        "  - Better path escaping for cross-platform compatibility",
        "  - Enhanced error handling and logging",
        "",
        "📋 To test subtitle visibility:",
        "  1. Create a new project with custom script",
        "  2. Use UPPERCASE text for better visibility",
        "  3. Enable subtitles (checked by default)",
        "  4. Generate audio and video",
        "  5. Check server logs for subtitle processing",
        "",
        "🎨 Subtitle appearance:",
        "  - White text with black outline",
        "  - Bottom-center positioning",
        "  - 28px font size for readability",
        "  - Speaker labels when multiple speakers",
        "",
        "🐛 If subtitles still not visible:",
        "  1. Check server logs for FFmpeg errors",
        "  2. Verify SRT file generation",
        "  3. Test with simple text first",
        "  4. Try different video players",
        "",
        "📱 Video player compatibility:",
        "  - HTML5 video players may not show embedded subtitles",
        "  - Download video and play in VLC or similar",
        "  - Subtitles are burned into video (not separate track)",
        "",
        "🎯 Expected result:",
        "  - Subtitles should be visible directly in video",
        "  - No need to enable captions in player",
        "  - Text appears at bottom of video frame"
    ]
    
    for tip in tips:
        print(tip)

if __name__ == "__main__":
    print("🎬 Subtitle Visibility Test")
    print("=" * 50)
    
    # Test subtitle generation
    visibility_works = test_subtitle_visibility()
    
    if visibility_works:
        # Create test project
        project_id = create_test_project_with_visible_subtitles()
        
        if project_id:
            # Show troubleshooting guide
            show_subtitle_troubleshooting()
            
            print(f"\n🎉 SUBTITLE VISIBILITY TEST COMPLETE!")
            print(f"🌐 Test project: http://localhost:5000/project/{project_id}")
            print(f"📋 Generate audio and video to test subtitle visibility!")
            print(f"🔍 Check server logs for subtitle processing details!")
        else:
            print("\n⚠️  Subtitle generation works but project creation failed")
    else:
        print("\n❌ Subtitle generation has issues")
        
    print(f"\n🌐 Visit: http://localhost:5000/create to test improved subtitles!")
