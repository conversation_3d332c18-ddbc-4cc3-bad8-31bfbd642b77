#!/usr/bin/env python3
"""
Database migration script to add font_size column to Project table
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def migrate_font_size():
    """Add font_size column to Project table"""
    try:
        from app import app, db, Project

        with app.app_context():
            print("🔧 Adding font_size column to Project table...")

            # Check if column already exists
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('project')]

            if 'font_size' in columns:
                print("✅ font_size column already exists")
                return True

            # Add the column using raw SQL
            try:
                with db.engine.connect() as conn:
                    conn.execute(db.text("ALTER TABLE project ADD COLUMN font_size VARCHAR(20) DEFAULT 'medium'"))
                    conn.commit()
                print("✅ Successfully added font_size column")

                # Update existing projects to have default font size
                projects = Project.query.all()
                for project in projects:
                    if not hasattr(project, 'font_size') or project.font_size is None:
                        project.font_size = 'medium'

                db.session.commit()
                print(f"✅ Updated {len(projects)} existing projects with default font size")

                return True

            except Exception as e:
                print(f"❌ Error adding column: {e}")
                return False

    except Exception as e:
        print(f"❌ Migration error: {e}")
        return False

def verify_migration():
    """Verify the migration was successful"""
    try:
        from app import app, db, Project

        with app.app_context():
            print("\n🔍 Verifying migration...")

            # Check if column exists
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('project')]

            if 'font_size' not in columns:
                print("❌ font_size column not found")
                return False

            print("✅ font_size column exists")

            # Check if existing projects have the field
            projects = Project.query.limit(5).all()
            for project in projects:
                if hasattr(project, 'font_size'):
                    print(f"✅ Project {project.id[:8]}... has font_size: {project.font_size}")
                else:
                    print(f"❌ Project {project.id[:8]}... missing font_size")
                    return False

            return True

    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def main():
    """Run the migration"""
    print("🎨 Font Size Column Migration")
    print("=" * 50)

    # Run migration
    if migrate_font_size():
        print("\n✅ Migration completed successfully")

        # Verify migration
        if verify_migration():
            print("\n🎉 Migration verified successfully!")
            print("\nℹ️  Font size control is now available:")
            print("   🔤 Small (20px) - Subtle captions")
            print("   🔤 Medium (28px) - Standard size")
            print("   🔤 Large (36px) - Social media")
            print("   🔤 Extra Large (44px) - Bold impact")
            print("   🔤 Huge (52px) - Maximum visibility")
        else:
            print("\n⚠️  Migration completed but verification failed")
            return False
    else:
        print("\n❌ Migration failed")
        return False

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
