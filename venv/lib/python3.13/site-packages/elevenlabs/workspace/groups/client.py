# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON>rapper, Sync<PERSON><PERSON>Wrapper
from ...core.request_options import RequestOptions
from ...types.workspace_group_by_name_response_model import WorkspaceGroupByNameResponseModel
from .members.client import Async<PERSON><PERSON>bersC<PERSON>, MembersClient
from .raw_client import AsyncRawGroupsClient, RawGroupsClient


class GroupsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawGroupsClient(client_wrapper=client_wrapper)
        self.members = MembersClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawGroupsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawGroupsClient
        """
        return self._raw_client

    def search(
        self, *, name: str, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[WorkspaceGroupByNameResponseModel]:
        """
        Searches for user groups in the workspace. Multiple or no groups may be returned.

        Parameters
        ----------
        name : str
            Name of the target group.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[WorkspaceGroupByNameResponseModel]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.groups.search(
            name="name",
        )
        """
        _response = self._raw_client.search(name=name, request_options=request_options)
        return _response.data


class AsyncGroupsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawGroupsClient(client_wrapper=client_wrapper)
        self.members = AsyncMembersClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawGroupsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawGroupsClient
        """
        return self._raw_client

    async def search(
        self, *, name: str, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.List[WorkspaceGroupByNameResponseModel]:
        """
        Searches for user groups in the workspace. Multiple or no groups may be returned.

        Parameters
        ----------
        name : str
            Name of the target group.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[WorkspaceGroupByNameResponseModel]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.groups.search(
                name="name",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.search(name=name, request_options=request_options)
        return _response.data
