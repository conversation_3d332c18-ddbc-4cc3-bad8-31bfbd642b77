# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import J<PERSON>NDecodeError

from ....core.api_error import ApiError
from ....core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ....core.http_response import Async<PERSON>ttp<PERSON><PERSON>ponse, HttpResponse
from ....core.jsonable_encoder import jsonable_encoder
from ....core.request_options import RequestOptions
from ....core.unchecked_base_model import construct_type
from ....errors.unprocessable_entity_error import UnprocessableEntityError
from ....types.add_workspace_group_member_response_model import AddWorkspaceGroupMemberResponseModel
from ....types.delete_workspace_group_member_response_model import DeleteWorkspaceGroupMemberResponseModel
from ....types.http_validation_error import HttpValidationError

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawMembersClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def remove(
        self, group_id: str, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[DeleteWorkspaceGroupMemberResponseModel]:
        """
        Removes a member from the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[DeleteWorkspaceGroupMemberResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/workspace/groups/{jsonable_encoder(group_id)}/members/remove",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DeleteWorkspaceGroupMemberResponseModel,
                    construct_type(
                        type_=DeleteWorkspaceGroupMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def add(
        self, group_id: str, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> HttpResponse[AddWorkspaceGroupMemberResponseModel]:
        """
        Adds a member of your workspace to the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[AddWorkspaceGroupMemberResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            f"v1/workspace/groups/{jsonable_encoder(group_id)}/members",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddWorkspaceGroupMemberResponseModel,
                    construct_type(
                        type_=AddWorkspaceGroupMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawMembersClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def remove(
        self, group_id: str, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[DeleteWorkspaceGroupMemberResponseModel]:
        """
        Removes a member from the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[DeleteWorkspaceGroupMemberResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/workspace/groups/{jsonable_encoder(group_id)}/members/remove",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    DeleteWorkspaceGroupMemberResponseModel,
                    construct_type(
                        type_=DeleteWorkspaceGroupMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def add(
        self, group_id: str, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> AsyncHttpResponse[AddWorkspaceGroupMemberResponseModel]:
        """
        Adds a member of your workspace to the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[AddWorkspaceGroupMemberResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"v1/workspace/groups/{jsonable_encoder(group_id)}/members",
            base_url=self._client_wrapper.get_environment().base,
            method="POST",
            json={
                "email": email,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddWorkspaceGroupMemberResponseModel,
                    construct_type(
                        type_=AddWorkspaceGroupMemberResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
