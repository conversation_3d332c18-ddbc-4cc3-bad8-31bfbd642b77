# This file was auto-generated by Fern from our API Definition.

import typing

BodyInviteUserV1WorkspaceInvitesAddPostWorkspacePermission = typing.Union[
    typing.Literal[
        "external",
        "admin",
        "workspace_admin",
        "workspace_member",
        "support_l1",
        "support_l2",
        "moderator",
        "sales",
        "voice_mixer",
        "voice_admin",
        "convai_admin",
        "enterprise_viewer",
        "quality_check_admin",
        "workspace_migration_admin",
        "human_reviewer",
        "productions_admin",
    ],
    typing.Any,
]
