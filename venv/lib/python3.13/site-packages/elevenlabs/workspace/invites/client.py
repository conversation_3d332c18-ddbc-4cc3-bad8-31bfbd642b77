# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON>rap<PERSON>, SyncC<PERSON><PERSON>rapper
from ...core.request_options import RequestOptions
from ...types.add_workspace_invite_response_model import AddWorkspaceInviteResponseModel
from ...types.delete_workspace_invite_response_model import DeleteWorkspaceInviteResponseModel
from .raw_client import AsyncRawInvitesClient, RawInvitesClient
from .types.body_invite_user_v_1_workspace_invites_add_post_workspace_permission import (
    BodyInviteUserV1WorkspaceInvitesAddPostWorkspacePermission,
)

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class InvitesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawInvitesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawInvitesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawInvitesClient
        """
        return self._raw_client

    def create(
        self,
        *,
        email: str,
        group_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        workspace_permission: typing.Optional[BodyInviteUserV1WorkspaceInvitesAddPostWorkspacePermission] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddWorkspaceInviteResponseModel:
        """
        Sends an email invitation to join your workspace to the provided email. If the user doesn't have an account they will be prompted to create one. If the user accepts this invite they will be added as a user to your workspace and your subscription using one of your seats. This endpoint may only be called by workspace administrators. If the user is already in the workspace a 400 error will be returned.

        Parameters
        ----------
        email : str
            The email of the customer

        group_ids : typing.Optional[typing.Sequence[str]]
            The group ids of the user

        workspace_permission : typing.Optional[BodyInviteUserV1WorkspaceInvitesAddPostWorkspacePermission]
            The workspace permission of the user

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.invites.create(
            email="<EMAIL>",
        )
        """
        _response = self._raw_client.create(
            email=email, group_ids=group_ids, workspace_permission=workspace_permission, request_options=request_options
        )
        return _response.data

    def create_batch(
        self,
        *,
        emails: typing.Sequence[str],
        group_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddWorkspaceInviteResponseModel:
        """
        Sends email invitations to join your workspace to the provided emails. Requires all email addresses to be part of a verified domain. If the users don't have an account they will be prompted to create one. If the users accept these invites they will be added as users to your workspace and your subscription using one of your seats. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        emails : typing.Sequence[str]
            The email of the customer

        group_ids : typing.Optional[typing.Sequence[str]]
            The group ids of the user

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.invites.create_batch(
            emails=["emails"],
        )
        """
        _response = self._raw_client.create_batch(emails=emails, group_ids=group_ids, request_options=request_options)
        return _response.data

    def delete(
        self, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteWorkspaceInviteResponseModel:
        """
        Invalidates an existing email invitation. The invitation will still show up in the inbox it has been delivered to, but activating it to join the workspace won't work. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            The email of the customer

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.invites.delete(
            email="<EMAIL>",
        )
        """
        _response = self._raw_client.delete(email=email, request_options=request_options)
        return _response.data


class AsyncInvitesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawInvitesClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawInvitesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawInvitesClient
        """
        return self._raw_client

    async def create(
        self,
        *,
        email: str,
        group_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        workspace_permission: typing.Optional[BodyInviteUserV1WorkspaceInvitesAddPostWorkspacePermission] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddWorkspaceInviteResponseModel:
        """
        Sends an email invitation to join your workspace to the provided email. If the user doesn't have an account they will be prompted to create one. If the user accepts this invite they will be added as a user to your workspace and your subscription using one of your seats. This endpoint may only be called by workspace administrators. If the user is already in the workspace a 400 error will be returned.

        Parameters
        ----------
        email : str
            The email of the customer

        group_ids : typing.Optional[typing.Sequence[str]]
            The group ids of the user

        workspace_permission : typing.Optional[BodyInviteUserV1WorkspaceInvitesAddPostWorkspacePermission]
            The workspace permission of the user

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.invites.create(
                email="<EMAIL>",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            email=email, group_ids=group_ids, workspace_permission=workspace_permission, request_options=request_options
        )
        return _response.data

    async def create_batch(
        self,
        *,
        emails: typing.Sequence[str],
        group_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddWorkspaceInviteResponseModel:
        """
        Sends email invitations to join your workspace to the provided emails. Requires all email addresses to be part of a verified domain. If the users don't have an account they will be prompted to create one. If the users accept these invites they will be added as users to your workspace and your subscription using one of your seats. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        emails : typing.Sequence[str]
            The email of the customer

        group_ids : typing.Optional[typing.Sequence[str]]
            The group ids of the user

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.invites.create_batch(
                emails=["emails"],
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create_batch(
            emails=emails, group_ids=group_ids, request_options=request_options
        )
        return _response.data

    async def delete(
        self, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteWorkspaceInviteResponseModel:
        """
        Invalidates an existing email invitation. The invitation will still show up in the inbox it has been delivered to, but activating it to join the workspace won't work. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        email : str
            The email of the customer

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteWorkspaceInviteResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.invites.delete(
                email="<EMAIL>",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(email=email, request_options=request_options)
        return _response.data
