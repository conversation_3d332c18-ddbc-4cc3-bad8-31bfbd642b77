# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class VerifiedVoiceLanguageResponseModel(UncheckedBaseModel):
    language: str = pydantic.Field()
    """
    The language of the voice.
    """

    model_id: str = pydantic.Field()
    """
    The voice's model ID.
    """

    accent: typing.Optional[str] = pydantic.Field(default=None)
    """
    The voice's accent, if applicable.
    """

    locale: typing.Optional[str] = pydantic.Field(default=None)
    """
    The voice's locale, if applicable.
    """

    preview_url: typing.Optional[str] = pydantic.Field(default=None)
    """
    The voice's preview URL, if applicable.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
