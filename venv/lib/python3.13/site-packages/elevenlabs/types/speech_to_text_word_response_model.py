# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .speech_to_text_character_response_model import SpeechToTextCharacterResponseModel
from .speech_to_text_word_response_model_type import SpeechToTextWordResponseModelType


class SpeechToTextWordResponseModel(UncheckedBaseModel):
    """
    Word-level detail of the transcription with timing information.
    """

    text: str = pydantic.Field()
    """
    The word or sound that was transcribed.
    """

    start: typing.Optional[float] = pydantic.Field(default=None)
    """
    The start time of the word or sound in seconds.
    """

    end: typing.Optional[float] = pydantic.Field(default=None)
    """
    The end time of the word or sound in seconds.
    """

    type: SpeechToTextWordResponseModelType = pydantic.Field()
    """
    The type of the word or sound. 'audio_event' is used for non-word sounds like laughter or footsteps.
    """

    speaker_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    Unique identifier for the speaker of this word.
    """

    logprob: float = pydantic.Field()
    """
    The log of the probability with which this word was predicted. Logprobs are in range [-infinity, 0], higher logprobs indicate a higher confidence the model has in its predictions.
    """

    characters: typing.Optional[typing.List[SpeechToTextCharacterResponseModel]] = pydantic.Field(default=None)
    """
    The characters that make up the word and their timing information.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
