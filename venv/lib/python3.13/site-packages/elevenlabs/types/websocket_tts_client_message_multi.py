# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.serialization import FieldMetadata
from ..core.unchecked_base_model import UncheckedBaseModel
from .generation_config import GenerationConfig
from .pronunciation_dictionary_locator import PronunciationDictionaryLocator
from .realtime_voice_settings import RealtimeVoiceSettings


class WebsocketTtsClientMessageMulti(UncheckedBaseModel):
    """
    Message sent from the client to the multi-context TTS WebSocket.
    """

    text: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text to be synthesized. 
    For the first message establishing a new context (identified by `context_id`, or a default context if `context_id` is absent), this should be a single space character (' '). 
    For subsequent messages to an active context, this is the text to synthesize. 
    This field can be null or an empty string if the message is primarily for control (e.g., using `flush`, `close_context`, or `close_socket`).
    """

    voice_settings: typing.Optional[RealtimeVoiceSettings] = pydantic.Field(default=None)
    """
    Voice settings. Can only be provided in the first message for a given context_id (or first message overall if context_id is not used/default).
    """

    generation_config: typing.Optional[GenerationConfig] = pydantic.Field(default=None)
    """
    Generation config. Can only be provided in the first message for a given context_id (or first message overall if context_id is not used/default).
    """

    xi_api_key: typing_extensions.Annotated[typing.Optional[str], FieldMetadata(alias="xi-api-key")] = pydantic.Field(
        default=None
    )
    """
    Your ElevenLabs API key. Can only be provided in the first message for a given context_id if not present in the header.
    """

    authorization: typing.Optional[str] = pydantic.Field(default=None)
    """
    Your authorization bearer token. Can only be provided in the first message for a given context_id if not present in the header.
    """

    flush: typing.Optional[bool] = pydantic.Field(default=None)
    """
    If true, flushes the audio buffer and returns the remaining audio for the specified `context_id`.
    """

    pronunciation_dictionary_locators: typing.Optional[typing.List[PronunciationDictionaryLocator]] = pydantic.Field(
        default=None
    )
    """
    Optional list of pronunciation dictionary locators. Can only be provided in the first message for a given context_id.
    """

    context_id: typing_extensions.Annotated[typing.Optional[str], FieldMetadata(alias="contextId")] = pydantic.Field(
        default=None
    )
    """
    An identifier for the text-to-speech context. Allows managing multiple independent audio generation streams over a single WebSocket connection. If omitted, a default context is used.
    """

    close_context: typing.Optional[bool] = pydantic.Field(default=None)
    """
    If true, closes the specified `contextId`. No further audio will be generated for this context. The `text` field is ignored.
    """

    close_socket: typing.Optional[bool] = pydantic.Field(default=None)
    """
    If true, flushes all contexts and closes the entire WebSocket connection. The `text` and `contextId` fields are ignored.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
