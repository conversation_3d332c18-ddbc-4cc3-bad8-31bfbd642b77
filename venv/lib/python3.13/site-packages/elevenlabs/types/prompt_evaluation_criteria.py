# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class PromptEvaluationCriteria(UncheckedBaseModel):
    """
    An evaluation using the transcript and a prompt for a yes/no achieved answer
    """

    id: str = pydantic.Field()
    """
    The unique identifier for the evaluation criteria
    """

    name: str
    type: typing.Optional[typing.Literal["prompt"]] = pydantic.Field(default=None)
    """
    The type of evaluation criteria
    """

    conversation_goal_prompt: str = pydantic.Field()
    """
    The prompt that the agent should use to evaluate the conversation
    """

    use_knowledge_base: typing.Optional[bool] = pydantic.Field(default=None)
    """
    When evaluating the prompt, should the agent's knowledge base be used.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
