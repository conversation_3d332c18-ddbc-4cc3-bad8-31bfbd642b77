# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .dynamic_variables_config import DynamicVariablesConfig
from .system_tool_config_output_params import SystemToolConfigOutputParams
from .webhook_tool_api_schema_config_output import WebhookToolApiSchemaConfigOutput


class PromptAgentOutputToolsItem_Client(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["client"] = "client"
    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    parameters: typing.Optional["ObjectJsonSchemaPropertyOutput"] = None
    expects_response: typing.Optional[bool] = None
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentOutputToolsItem_Mcp(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["mcp"] = "mcp"
    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    parameters: typing.Optional["ObjectJsonSchemaPropertyOutput"] = None
    mcp_tool_name: str
    mcp_server_id: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentOutputToolsItem_NativeMcp(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["native_mcp"] = "native_mcp"
    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    parameters: typing.Optional["ObjectJsonSchemaPropertyOutput"] = None
    mcp_tool_name: str
    mcp_server_id: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentOutputToolsItem_System(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["system"] = "system"
    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    params: SystemToolConfigOutputParams

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentOutputToolsItem_Webhook(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["webhook"] = "webhook"
    id: typing.Optional[str] = None
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    api_schema: WebhookToolApiSchemaConfigOutput
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_output import ArrayJsonSchemaPropertyOutput  # noqa: E402, F401, I001
from .object_json_schema_property_output import ObjectJsonSchemaPropertyOutput  # noqa: E402, F401, I001

PromptAgentOutputToolsItem = typing_extensions.Annotated[
    typing.Union[
        PromptAgentOutputToolsItem_Client,
        PromptAgentOutputToolsItem_Mcp,
        PromptAgentOutputToolsItem_NativeMcp,
        PromptAgentOutputToolsItem_System,
        PromptAgentOutputToolsItem_Webhook,
    ],
    UnionMetadata(discriminant="type"),
]
update_forward_refs(PromptAgentOutputToolsItem_Client)
update_forward_refs(PromptAgentOutputToolsItem_Mcp)
update_forward_refs(PromptAgentOutputToolsItem_NativeMcp)
update_forward_refs(PromptAgentOutputToolsItem_Webhook)
