# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .sip_media_encryption_enum import SipMediaEncryptionEnum
from .sip_trunk_transport_enum import SipTrunkTransportEnum


class SipTrunkConfigResponseModel(UncheckedBaseModel):
    """
    SIP Trunk configuration details for a phone number
    """

    address: str = pydantic.Field()
    """
    Hostname or IP the SIP INVITE is sent to
    """

    transport: SipTrunkTransportEnum = pydantic.Field()
    """
    Protocol to use for SIP transport
    """

    media_encryption: SipMediaEncryptionEnum = pydantic.Field()
    """
    Whether or not to encrypt media (data layer).
    """

    headers: typing.Optional[typing.Dict[str, str]] = pydantic.Field(default=None)
    """
    SIP headers for INVITE request
    """

    has_auth_credentials: bool = pydantic.Field()
    """
    Whether authentication credentials are configured
    """

    username: typing.Optional[str] = pydantic.Field(default=None)
    """
    SIP trunk username (if available)
    """

    has_outbound_trunk: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether a LiveKit SIP outbound trunk is configured
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
