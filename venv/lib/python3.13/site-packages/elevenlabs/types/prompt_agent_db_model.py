# This file was auto-generated by Fern from our API Definition.

from __future__ import annotations

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel
from .custom_llm import <PERSON><PERSON><PERSON>
from .knowledge_base_locator import KnowledgeBaseLocator
from .llm import Llm
from .prompt_agent_db_model_tools_item import PromptAgentDbModelToolsItem
from .rag_config import RagConfig


class PromptAgentDbModel(UncheckedBaseModel):
    prompt: typing.Optional[str] = pydantic.Field(default=None)
    """
    The prompt for the agent
    """

    llm: typing.Optional[Llm] = pydantic.Field(default=None)
    """
    The LLM to query with the prompt and the chat history
    """

    temperature: typing.Optional[float] = pydantic.Field(default=None)
    """
    The temperature for the LLM
    """

    max_tokens: typing.Optional[int] = pydantic.Field(default=None)
    """
    If greater than 0, maximum number of tokens the LLM can predict
    """

    tools: typing.Optional[typing.List[PromptAgentDbModelToolsItem]] = pydantic.Field(default=None)
    """
    A list of tools that the agent can use over the course of the conversation
    """

    tool_ids: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    A list of IDs of tools used by the agent
    """

    mcp_server_ids: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    A list of MCP server ids to be used by the agent
    """

    native_mcp_server_ids: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    A list of Native MCP server ids to be used by the agent
    """

    knowledge_base: typing.Optional[typing.List[KnowledgeBaseLocator]] = pydantic.Field(default=None)
    """
    A list of knowledge bases to be used by the agent
    """

    custom_llm: typing.Optional[CustomLlm] = pydantic.Field(default=None)
    """
    Definition for a custom LLM if LLM field is set to 'CUSTOM_LLM'
    """

    ignore_default_personality: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to ignore the default personality
    """

    rag: typing.Optional[RagConfig] = pydantic.Field(default=None)
    """
    Configuration for RAG
    """

    knowledge_base_document_ids: typing.Optional[typing.List[str]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput  # noqa: E402, F401, I001
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput  # noqa: E402, F401, I001

update_forward_refs(PromptAgentDbModel)
