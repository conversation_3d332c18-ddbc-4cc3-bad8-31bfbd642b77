# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.edit_voice_settings_response_model import EditVoiceSettingsResponseModel
from ...types.voice_settings import VoiceSettings
from .raw_client import AsyncRawSettingsClient, RawSettingsClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SettingsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSettingsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSettingsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSettingsClient
        """
        return self._raw_client

    def get_default(self, *, request_options: typing.Optional[RequestOptions] = None) -> VoiceSettings:
        """
        Gets the default settings for voices. "similarity_boost" corresponds to"Clarity + Similarity Enhancement" in the web app and "stability" corresponds to "Stability" slider in the web app.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceSettings
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.settings.get_default()
        """
        _response = self._raw_client.get_default(request_options=request_options)
        return _response.data

    def get(self, voice_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> VoiceSettings:
        """
        Returns the settings for a specific voice. "similarity_boost" corresponds to"Clarity + Similarity Enhancement" in the web app and "stability" corresponds to "Stability" slider in the web app.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceSettings
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.settings.get(
            voice_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get(voice_id, request_options=request_options)
        return _response.data

    def update(
        self, voice_id: str, *, request: VoiceSettings, request_options: typing.Optional[RequestOptions] = None
    ) -> EditVoiceSettingsResponseModel:
        """
        Edit your settings for a specific voice. "similarity_boost" corresponds to "Clarity + Similarity Enhancement" in the web app and "stability" corresponds to "Stability" slider in the web app.

        Parameters
        ----------
        voice_id : str
            ID of the voice to be used. You can use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        request : VoiceSettings

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditVoiceSettingsResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs, VoiceSettings

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.settings.update(
            voice_id="21m00Tcm4TlvDq8ikWAM",
            request=VoiceSettings(
                stability=1.0,
                similarity_boost=1.0,
                style=0.0,
                use_speaker_boost=True,
                speed=1.0,
            ),
        )
        """
        _response = self._raw_client.update(voice_id, request=request, request_options=request_options)
        return _response.data


class AsyncSettingsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSettingsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSettingsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSettingsClient
        """
        return self._raw_client

    async def get_default(self, *, request_options: typing.Optional[RequestOptions] = None) -> VoiceSettings:
        """
        Gets the default settings for voices. "similarity_boost" corresponds to"Clarity + Similarity Enhancement" in the web app and "stability" corresponds to "Stability" slider in the web app.

        Parameters
        ----------
        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceSettings
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.settings.get_default()


        asyncio.run(main())
        """
        _response = await self._raw_client.get_default(request_options=request_options)
        return _response.data

    async def get(self, voice_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> VoiceSettings:
        """
        Returns the settings for a specific voice. "similarity_boost" corresponds to"Clarity + Similarity Enhancement" in the web app and "stability" corresponds to "Stability" slider in the web app.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceSettings
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.settings.get(
                voice_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(voice_id, request_options=request_options)
        return _response.data

    async def update(
        self, voice_id: str, *, request: VoiceSettings, request_options: typing.Optional[RequestOptions] = None
    ) -> EditVoiceSettingsResponseModel:
        """
        Edit your settings for a specific voice. "similarity_boost" corresponds to "Clarity + Similarity Enhancement" in the web app and "stability" corresponds to "Stability" slider in the web app.

        Parameters
        ----------
        voice_id : str
            ID of the voice to be used. You can use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.

        request : VoiceSettings

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EditVoiceSettingsResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs, VoiceSettings

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.settings.update(
                voice_id="21m00Tcm4TlvDq8ikWAM",
                request=VoiceSettings(
                    stability=1.0,
                    similarity_boost=1.0,
                    style=0.0,
                    use_speaker_boost=True,
                    speed=1.0,
                ),
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(voice_id, request=request, request_options=request_options)
        return _response.data
