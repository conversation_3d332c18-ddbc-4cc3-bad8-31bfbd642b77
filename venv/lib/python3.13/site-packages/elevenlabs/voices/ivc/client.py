# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ... import core
from ...core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.add_voice_ivc_response_model import AddVoiceIvcResponseModel
from .raw_client import Async<PERSON>aw<PERSON>vc<PERSON>lient, RawIvcClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class IvcClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawIvcClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawIvcClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawIvcClient
        """
        return self._raw_client

    def create(
        self,
        *,
        name: str,
        files: typing.List[core.File],
        remove_background_noise: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        labels: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddVoiceIvcResponseModel:
        """
        Create a voice clone and add it to your Voices

        Parameters
        ----------
        name : str
            The name that identifies this voice. This will be displayed in the dropdown of the website.

        files : typing.List[core.File]
            See core.File for more documentation

        remove_background_noise : typing.Optional[bool]
            If set will remove background noise for voice samples using our audio isolation model. If the samples do not include background noise, it can make the quality worse.

        description : typing.Optional[str]
            A description of the voice.

        labels : typing.Optional[str]
            Serialized labels dictionary for the voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddVoiceIvcResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.ivc.create(
            name="name",
        )
        """
        _response = self._raw_client.create(
            name=name,
            files=files,
            remove_background_noise=remove_background_noise,
            description=description,
            labels=labels,
            request_options=request_options,
        )
        return _response.data


class AsyncIvcClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawIvcClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawIvcClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawIvcClient
        """
        return self._raw_client

    async def create(
        self,
        *,
        name: str,
        files: typing.List[core.File],
        remove_background_noise: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        labels: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddVoiceIvcResponseModel:
        """
        Create a voice clone and add it to your Voices

        Parameters
        ----------
        name : str
            The name that identifies this voice. This will be displayed in the dropdown of the website.

        files : typing.List[core.File]
            See core.File for more documentation

        remove_background_noise : typing.Optional[bool]
            If set will remove background noise for voice samples using our audio isolation model. If the samples do not include background noise, it can make the quality worse.

        description : typing.Optional[str]
            A description of the voice.

        labels : typing.Optional[str]
            Serialized labels dictionary for the voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddVoiceIvcResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.ivc.create(
                name="name",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            name=name,
            files=files,
            remove_background_noise=remove_background_noise,
            description=description,
            labels=labels,
            request_options=request_options,
        )
        return _response.data
