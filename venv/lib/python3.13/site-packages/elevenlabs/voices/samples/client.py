# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from .audio.client import AsyncAudio<PERSON>lient, AudioClient
from .raw_client import AsyncRawSamplesClient, RawSamplesClient


class SamplesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSamplesClient(client_wrapper=client_wrapper)
        self.audio = AudioClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSamplesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSamplesClient
        """
        return self._raw_client


class AsyncSamplesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSamplesClient(client_wrapper=client_wrapper)
        self.audio = AsyncAudioClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSamplesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSamplesClient
        """
        return self._raw_client
