# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .....core.client_wrapper import AsyncC<PERSON><PERSON>rapper, SyncClientWrapper
from .....core.request_options import RequestOptions
from .....types.voice_sample_visual_waveform_response_model import VoiceSampleVisualWaveformResponseModel
from .raw_client import AsyncRaw<PERSON>aveformClient, RawWaveformClient


class WaveformClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawWaveformClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawWaveformClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawWaveformClient
        """
        return self._raw_client

    def get(
        self, voice_id: str, sample_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> VoiceSampleVisualWaveformResponseModel:
        """
        Retrieve the visual waveform of a voice sample.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        sample_id : str
            Sample ID to be used

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceSampleVisualWaveformResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.samples.waveform.get(
            voice_id="21m00Tcm4TlvDq8ikWAM",
            sample_id="VW7YKqPnjY4h39yTbx2L",
        )
        """
        _response = self._raw_client.get(voice_id, sample_id, request_options=request_options)
        return _response.data


class AsyncWaveformClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawWaveformClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawWaveformClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawWaveformClient
        """
        return self._raw_client

    async def get(
        self, voice_id: str, sample_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> VoiceSampleVisualWaveformResponseModel:
        """
        Retrieve the visual waveform of a voice sample.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        sample_id : str
            Sample ID to be used

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceSampleVisualWaveformResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.samples.waveform.get(
                voice_id="21m00Tcm4TlvDq8ikWAM",
                sample_id="VW7YKqPnjY4h39yTbx2L",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(voice_id, sample_id, request_options=request_options)
        return _response.data
