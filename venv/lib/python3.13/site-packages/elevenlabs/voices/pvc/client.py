# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ...core.request_options import RequestOptions
from ...types.add_voice_response_model import AddVoiceResponseModel
from ...types.start_pvc_voice_training_response_model import StartPvcVoiceTrainingResponseModel
from .raw_client import AsyncRawPvcClient, RawPvcClient
from .samples.client import AsyncSamplesClient, SamplesClient
from .verification.client import AsyncVerificationClient, VerificationClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class PvcClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawPvcClient(client_wrapper=client_wrapper)
        self.samples = SamplesClient(client_wrapper=client_wrapper)

        self.verification = VerificationClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawPvcClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawPvcClient
        """
        return self._raw_client

    def create(
        self,
        *,
        name: str,
        language: str,
        description: typing.Optional[str] = OMIT,
        labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddVoiceResponseModel:
        """
        Creates a new PVC voice with metadata but no samples

        Parameters
        ----------
        name : str
            The name that identifies this voice. This will be displayed in the dropdown of the website.

        language : str
            Language used in the samples.

        description : typing.Optional[str]
            Description to use for the created voice.

        labels : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            Serialized labels dictionary for the voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddVoiceResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.create(
            name="John Smith",
            language="en",
        )
        """
        _response = self._raw_client.create(
            name=name, language=language, description=description, labels=labels, request_options=request_options
        )
        return _response.data

    def update(
        self,
        voice_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        language: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddVoiceResponseModel:
        """
        Edit PVC voice metadata

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        name : typing.Optional[str]
            The name that identifies this voice. This will be displayed in the dropdown of the website.

        language : typing.Optional[str]
            Language used in the samples.

        description : typing.Optional[str]
            Description to use for the created voice.

        labels : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            Serialized labels dictionary for the voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddVoiceResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.update(
            voice_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.update(
            voice_id,
            name=name,
            language=language,
            description=description,
            labels=labels,
            request_options=request_options,
        )
        return _response.data

    def train(
        self,
        voice_id: str,
        *,
        model_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> StartPvcVoiceTrainingResponseModel:
        """
        Start PVC training process for a voice.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        model_id : typing.Optional[str]
            The model ID to use for the conversion.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        StartPvcVoiceTrainingResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.train(
            voice_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.train(voice_id, model_id=model_id, request_options=request_options)
        return _response.data


class AsyncPvcClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawPvcClient(client_wrapper=client_wrapper)
        self.samples = AsyncSamplesClient(client_wrapper=client_wrapper)

        self.verification = AsyncVerificationClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawPvcClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawPvcClient
        """
        return self._raw_client

    async def create(
        self,
        *,
        name: str,
        language: str,
        description: typing.Optional[str] = OMIT,
        labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddVoiceResponseModel:
        """
        Creates a new PVC voice with metadata but no samples

        Parameters
        ----------
        name : str
            The name that identifies this voice. This will be displayed in the dropdown of the website.

        language : str
            Language used in the samples.

        description : typing.Optional[str]
            Description to use for the created voice.

        labels : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            Serialized labels dictionary for the voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddVoiceResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.create(
                name="John Smith",
                language="en",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            name=name, language=language, description=description, labels=labels, request_options=request_options
        )
        return _response.data

    async def update(
        self,
        voice_id: str,
        *,
        name: typing.Optional[str] = OMIT,
        language: typing.Optional[str] = OMIT,
        description: typing.Optional[str] = OMIT,
        labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddVoiceResponseModel:
        """
        Edit PVC voice metadata

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        name : typing.Optional[str]
            The name that identifies this voice. This will be displayed in the dropdown of the website.

        language : typing.Optional[str]
            Language used in the samples.

        description : typing.Optional[str]
            Description to use for the created voice.

        labels : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            Serialized labels dictionary for the voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddVoiceResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.update(
                voice_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(
            voice_id,
            name=name,
            language=language,
            description=description,
            labels=labels,
            request_options=request_options,
        )
        return _response.data

    async def train(
        self,
        voice_id: str,
        *,
        model_id: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> StartPvcVoiceTrainingResponseModel:
        """
        Start PVC training process for a voice.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        model_id : typing.Optional[str]
            The model ID to use for the conversion.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        StartPvcVoiceTrainingResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.train(
                voice_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.train(voice_id, model_id=model_id, request_options=request_options)
        return _response.data
