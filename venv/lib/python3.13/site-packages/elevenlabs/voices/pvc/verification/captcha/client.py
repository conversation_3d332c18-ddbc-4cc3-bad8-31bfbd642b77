# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ..... import core
from .....core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from .....core.request_options import RequestOptions
from .....types.verify_pvc_voice_captcha_response_model import VerifyPvcVoiceCaptchaResponseModel
from .raw_client import AsyncRawCaptchaClient, RawCaptchaClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class CaptchaClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawCaptchaClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawCaptchaClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawCaptchaClient
        """
        return self._raw_client

    def get(self, voice_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Get captcha for PVC voice verification.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.verification.captcha.get(
            voice_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.get(voice_id, request_options=request_options)
        return _response.data

    def verify(
        self, voice_id: str, *, recording: core.File, request_options: typing.Optional[RequestOptions] = None
    ) -> VerifyPvcVoiceCaptchaResponseModel:
        """
        Submit captcha verification for PVC voice.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        recording : core.File
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VerifyPvcVoiceCaptchaResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.verification.captcha.verify(
            voice_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.verify(voice_id, recording=recording, request_options=request_options)
        return _response.data


class AsyncCaptchaClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawCaptchaClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawCaptchaClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawCaptchaClient
        """
        return self._raw_client

    async def get(self, voice_id: str, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Get captcha for PVC voice verification.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.verification.captcha.get(
                voice_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(voice_id, request_options=request_options)
        return _response.data

    async def verify(
        self, voice_id: str, *, recording: core.File, request_options: typing.Optional[RequestOptions] = None
    ) -> VerifyPvcVoiceCaptchaResponseModel:
        """
        Submit captcha verification for PVC voice.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        recording : core.File
            See core.File for more documentation

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VerifyPvcVoiceCaptchaResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.verification.captcha.verify(
                voice_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.verify(voice_id, recording=recording, request_options=request_options)
        return _response.data
