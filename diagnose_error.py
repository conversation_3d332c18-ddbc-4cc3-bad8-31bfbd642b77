#!/usr/bin/env python3
"""
Diagnose errors in the AI Podcast Generator
"""

import os
import sys

def check_environment():
    """Check environment variables"""
    print("🔍 Checking Environment Variables...")
    
    required_vars = {
        'OPENAI_API_KEY': 'OpenAI API for script generation and TTS',
        'PEXELS_API_KEY': 'Pexels API for video search',
        'SECRET_KEY': 'Flask secret key for sessions'
    }
    
    issues = []
    
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value:
            issues.append(f"❌ {var}: Not set")
        elif value.startswith('your_') or 'placeholder' in value.lower():
            issues.append(f"⚠️  {var}: Using placeholder value")
        else:
            print(f"✅ {var}: Configured ({value[:10]}...)")
    
    for issue in issues:
        print(issue)
    
    return len(issues) == 0

def check_database():
    """Check database configuration"""
    print("\n💾 Checking Database...")
    
    try:
        from app import app, db, SystemSettings, Project
        
        with app.app_context():
            # Check if tables exist
            try:
                project_count = Project.query.count()
                settings_count = SystemSettings.query.count()
                print(f"✅ Database connected: {project_count} projects, {settings_count} settings")
                
                # Check for auto_create_video column
                projects = Project.query.all()
                if projects:
                    project = projects[0]
                    if hasattr(project, 'auto_create_video'):
                        print("✅ Database migration: auto_create_video column exists")
                    else:
                        print("❌ Database migration: auto_create_video column missing")
                        return False
                
                return True
                
            except Exception as e:
                print(f"❌ Database error: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def check_api_keys():
    """Check API keys in database"""
    print("\n🔑 Checking API Keys in Database...")
    
    try:
        from app import app, SystemSettings
        
        with app.app_context():
            # Check OpenAI API key
            openai_setting = SystemSettings.query.filter_by(key='openai_api_key').first()
            if openai_setting and openai_setting.value:
                if openai_setting.value.startswith('sk-'):
                    print(f"✅ OpenAI API key: Properly formatted ({openai_setting.value[:10]}...)")
                else:
                    print(f"⚠️  OpenAI API key: Unusual format ({openai_setting.value[:10]}...)")
            else:
                print("❌ OpenAI API key: Not found in database")
            
            # Check Pexels API key
            pexels_setting = SystemSettings.query.filter_by(key='pexels_api_key').first()
            if pexels_setting and pexels_setting.value:
                print(f"✅ Pexels API key: Configured ({pexels_setting.value[:10]}...)")
            else:
                print("❌ Pexels API key: Not found in database")
                
            return True
            
    except Exception as e:
        print(f"❌ Error checking API keys: {e}")
        return False

def check_file_permissions():
    """Check file and directory permissions"""
    print("\n📁 Checking File Permissions...")
    
    directories = ['uploads', 'uploads/audio', 'uploads/video', 'uploads/final']
    
    for directory in directories:
        if os.path.exists(directory):
            if os.access(directory, os.W_OK):
                print(f"✅ {directory}: Writable")
            else:
                print(f"❌ {directory}: Not writable")
                return False
        else:
            print(f"⚠️  {directory}: Does not exist")
    
    return True

def check_dependencies():
    """Check required dependencies"""
    print("\n📦 Checking Dependencies...")
    
    dependencies = {
        'flask': 'Web framework',
        'openai': 'OpenAI API client',
        'requests': 'HTTP requests',
        'PIL': 'Image processing',
        'ffmpeg': 'Video processing (system command)'
    }
    
    missing = []
    
    for dep, description in dependencies.items():
        try:
            if dep == 'ffmpeg':
                import subprocess
                result = subprocess.run(['ffmpeg', '-version'], capture_output=True, timeout=5)
                if result.returncode == 0:
                    print(f"✅ {dep}: Available")
                else:
                    missing.append(f"❌ {dep}: Not working")
            else:
                __import__(dep)
                print(f"✅ {dep}: Installed")
        except Exception:
            missing.append(f"❌ {dep}: Missing or not working")
    
    for miss in missing:
        print(miss)
    
    return len(missing) == 0

def provide_solutions():
    """Provide solutions for common issues"""
    print("\n" + "="*60)
    print("🛠️  COMMON SOLUTIONS")
    print("="*60)
    
    solutions = [
        "🔑 OpenAI API Key Issues:",
        "   1. Get your API key from: https://platform.openai.com/account/api-keys",
        "   2. Run: python setup_openai_key.py",
        "   3. Or manually edit .env file",
        "",
        "💾 Database Issues:",
        "   1. Run: python migrate_database.py",
        "   2. Or delete instance/podcast_generator.db and restart",
        "",
        "📦 Dependency Issues:",
        "   1. Install FFmpeg: brew install ffmpeg (Mac) or apt install ffmpeg (Linux)",
        "   2. Install Python packages: pip install -r requirements.txt",
        "",
        "🔄 General Issues:",
        "   1. Restart the server: Ctrl+C then python run.py",
        "   2. Clear browser cache",
        "   3. Check server logs for specific errors",
        "",
        "🆘 Still having issues?",
        "   1. Check the terminal output for specific error messages",
        "   2. Make sure all API keys are valid",
        "   3. Ensure you have internet connection for API calls"
    ]
    
    for solution in solutions:
        print(solution)

if __name__ == "__main__":
    print("🔍 AI Podcast Generator - Error Diagnosis")
    print("=" * 60)
    
    # Run all checks
    env_ok = check_environment()
    db_ok = check_database()
    api_ok = check_api_keys()
    files_ok = check_file_permissions()
    deps_ok = check_dependencies()
    
    # Summary
    checks = [env_ok, db_ok, api_ok, files_ok, deps_ok]
    passed = sum(checks)
    total = len(checks)
    
    print(f"\n📊 DIAGNOSIS SUMMARY: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All checks passed! The system should be working.")
        print("💡 If you're still seeing errors, check the server logs for specific issues.")
    else:
        print("⚠️  Some issues found. See solutions below.")
        provide_solutions()
    
    print(f"\n🌐 Server should be running at: http://localhost:5000")
    print("📋 Check the terminal where you ran 'python run.py' for real-time error messages.")
