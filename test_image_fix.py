#!/usr/bin/env python3
"""
Test script to verify the image serving fix
"""

import os
import sys
import requests

def test_image_serving():
    """Test if uploaded images can be served correctly"""
    print("🖼️  Testing Image Serving Fix...")
    print("=" * 50)
    
    # Check if the image file exists
    image_filename = "3c483540-8d2d-4932-ace1-4d4f388e5800.jpg"
    image_path = f"uploads/images/{image_filename}"
    
    if os.path.exists(image_path):
        print(f"✅ Image file exists: {image_path}")
        size = os.path.getsize(image_path)
        print(f"📏 File size: {size} bytes")
    else:
        print(f"❌ Image file not found: {image_path}")
        return False
    
    # Check database for project with this image
    try:
        from app import app, db, Project, User
        
        with app.app_context():
            project = Project.query.filter_by(user_media_file=image_filename).first()
            
            if project:
                print(f"✅ Project found with image: {project.title}")
                print(f"👤 User ID: {project.user_id}")
                print(f"📁 Media type: {project.user_media_type}")
                print(f"📊 Status: {project.status}")
                
                # Get user info
                user = User.query.get(project.user_id)
                if user:
                    print(f"👤 Username: {user.username}")
                else:
                    print("❌ User not found")
                    
                return True
            else:
                print(f"❌ No project found with image: {image_filename}")
                return False
                
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_route_functionality():
    """Test if the new image serving route works"""
    print("\n🌐 Testing Route Functionality...")
    print("=" * 50)
    
    try:
        # Test the route (will redirect to login but should not 404)
        image_filename = "3c483540-8d2d-4932-ace1-4d4f388e5800.jpg"
        url = f"http://localhost:5000/uploads/images/{image_filename}"
        
        response = requests.get(url, allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ Route exists and redirects to login (expected)")
            print(f"📍 Redirect location: {response.headers.get('Location', 'Not found')}")
            return True
        elif response.status_code == 404:
            print("❌ Route returns 404 - route not working")
            return False
        else:
            print(f"⚠️  Unexpected status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server - make sure it's running")
        return False
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

def check_template_fix():
    """Check if the template uses the correct path"""
    print("\n📄 Checking Template Fix...")
    print("=" * 50)
    
    try:
        with open('templates/project_detail.html', 'r') as f:
            content = f.read()
            
        # Check if the path was fixed
        if '/uploads/images/' in content:
            print("✅ Template uses correct path: /uploads/images/")
        else:
            print("❌ Template still uses wrong path")
            return False
            
        # Check if old path is removed
        if '/static/uploads/images/' in content:
            print("❌ Template still contains old path: /static/uploads/images/")
            return False
        else:
            print("✅ Old incorrect path removed")
            
        return True
        
    except Exception as e:
        print(f"❌ Error reading template: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 Image Serving Fix Test")
    print("=" * 50)
    
    tests = [
        ("Image File Check", test_image_serving),
        ("Route Functionality", test_route_functionality), 
        ("Template Fix", check_template_fix)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Image serving should now work correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
