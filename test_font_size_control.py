#!/usr/bin/env python3
"""
Test script to verify font size control functionality
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_font_size_mapping():
    """Test the font size mapping in video processor"""
    print("🎨 Testing Font Size Mapping...")

    try:
        from video_processor import VideoProcessor

        vp = VideoProcessor()

        # Test all font size options
        font_sizes = {
            'small': 20,
            'medium': 28,
            'large': 36,
            'extra_large': 44,
            'huge': 52
        }

        for size_name, expected_px in font_sizes.items():
            config = vp.get_subtitle_style_config('white_black_outline', 'script', size_name)

            # Extract font size from config
            if f'FontSize={expected_px}' in config:
                print(f"✅ {size_name}: {expected_px}px - Correct")
            else:
                print(f"❌ {size_name}: Expected {expected_px}px but got different value")
                print(f"   Config: {config}")
                return False

        return True

    except Exception as e:
        print(f"❌ Error testing font size mapping: {e}")
        return False

def test_database_integration():
    """Test that projects can store font size"""
    print("\n💾 Testing Database Integration...")

    try:
        from app import app, db, Project

        with app.app_context():
            # Check existing projects have font_size
            projects = Project.query.limit(3).all()

            for project in projects:
                if hasattr(project, 'font_size') and project.font_size:
                    print(f"✅ Project {project.title[:20]}... has font_size: {project.font_size}")
                else:
                    print(f"❌ Project {project.title[:20]}... missing font_size")
                    return False

            return True

    except Exception as e:
        print(f"❌ Error testing database integration: {e}")
        return False

def test_form_field():
    """Test that the form field is properly configured"""
    print("\n📝 Testing Form Field...")

    try:
        from app import app, ProjectForm

        with app.app_context():
            form = ProjectForm()

        # Check if font_size field exists
        if hasattr(form, 'font_size'):
            print("✅ Form has font_size field")

            # Check choices
            choices = form.font_size.choices
            expected_choices = ['small', 'medium', 'large', 'extra_large', 'huge']

            choice_values = [choice[0] for choice in choices]

            for expected in expected_choices:
                if expected in choice_values:
                    print(f"✅ Choice '{expected}' available")
                else:
                    print(f"❌ Choice '{expected}' missing")
                    return False

            # Check default value
            if form.font_size.default == 'medium':
                print("✅ Default value is 'medium'")
            else:
                print(f"❌ Default value is '{form.font_size.default}', expected 'medium'")
                return False

            return True
        else:
            print("❌ Form missing font_size field")
            return False

    except Exception as e:
        print(f"❌ Error testing form field: {e}")
        return False

def test_style_variations():
    """Test font size with different styles"""
    print("\n🎨 Testing Style Variations...")

    try:
        from video_processor import VideoProcessor

        vp = VideoProcessor()

        styles = ['white_black_outline', 'black_white_outline', 'yellow_black_outline', 'white_shadow', 'bold_white']
        font_sizes = ['small', 'medium', 'large', 'extra_large', 'huge']

        for style in styles:
            for font_size in font_sizes:
                config = vp.get_subtitle_style_config(style, 'script', font_size)

                # Check that config contains expected elements
                if 'FontSize=' in config and 'FontName=' in config:
                    print(f"✅ {style} + {font_size}: Valid config generated")
                else:
                    print(f"❌ {style} + {font_size}: Invalid config")
                    print(f"   Config: {config}")
                    return False

        return True

    except Exception as e:
        print(f"❌ Error testing style variations: {e}")
        return False

def show_font_size_examples():
    """Show examples of different font sizes"""
    print("\n📏 Font Size Examples:")
    print("=" * 50)

    font_sizes = {
        'small': (20, 'Subtle captions for minimal distraction'),
        'medium': (28, 'Standard readable size for most content'),
        'large': (36, 'Social media friendly, good visibility'),
        'extra_large': (44, 'Bold impact for important content'),
        'huge': (52, 'Maximum visibility for accessibility')
    }

    for size_name, (px, description) in font_sizes.items():
        print(f"🔤 {size_name.upper():<12} {px}px - {description}")

def main():
    """Run all tests"""
    print("🎨 Font Size Control Test Suite")
    print("=" * 50)

    tests = [
        ("Font Size Mapping", test_font_size_mapping),
        ("Database Integration", test_database_integration),
        ("Form Field", test_form_field),
        ("Style Variations", test_style_variations)
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, False))

    # Show examples
    show_font_size_examples()

    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)

    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 Results: {passed}/{len(results)} tests passed")

    if passed == len(results):
        print("\n🎉 All tests passed! Font size control is working correctly.")
        print("\n💡 How to use:")
        print("   1. Go to project creation page")
        print("   2. Enable 'Include subtitles/captions in video'")
        print("   3. Choose your preferred font size from the dropdown")
        print("   4. Create your project and generate video")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")

    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
