#!/usr/bin/env python3
"""
Test the full workflow: keyword -> script -> audio
"""

import os
import sys

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_script_generation():
    """Test script generation from keyword"""
    print("🎯 Testing Script Generation...")
    
    try:
        from ai_services import ScriptGenerator
        
        generator = ScriptGenerator()
        script = generator.generate_script(
            keyword="artificial intelligence",
            language="en",
            num_speakers=2,
            tone="conversational"
        )
        
        if script:
            print(f"✅ Script generated successfully!")
            print(f"Script length: {len(script)} characters")
            print(f"Script preview: {script[:200]}...")
            return script
        else:
            print("❌ Failed to generate script")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_audio_generation(script):
    """Test audio generation from script"""
    print("\n🎙️ Testing Audio Generation...")
    
    try:
        from ai_services import VoiceGenerator
        
        generator = VoiceGenerator()
        output_path = "test_full_workflow_audio.mp3"
        
        voice_selections = {
            'speaker_1_voice': '0',  # Alloy
            'speaker_2_voice': '1'   # Echo
        }
        
        success = generator.generate_audio_from_script(
            script=script,
            output_path=output_path,
            language="en",
            voice_selections=voice_selections
        )
        
        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ Audio generated successfully!")
            print(f"Output file: {output_path}")
            print(f"File size: {file_size} bytes")
            return True
        else:
            print("❌ Failed to generate audio")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_custom_script():
    """Test with a custom script"""
    print("\n📝 Testing Custom Script...")
    
    custom_script = """Speaker 1: Welcome to our AI podcast! I'm your host Sarah.
Speaker 2: Thanks for having me, Sarah. I'm excited to discuss artificial intelligence.
Speaker 1: Let's start with the basics. What exactly is AI?
Speaker 2: AI is the simulation of human intelligence in machines that are programmed to think and learn.
Speaker 1: That's fascinating! How is AI changing our daily lives?
Speaker 2: From voice assistants to recommendation systems, AI is everywhere around us."""
    
    try:
        from ai_services import VoiceGenerator
        
        generator = VoiceGenerator()
        output_path = "test_custom_script_audio.mp3"
        
        voice_selections = {
            'speaker_1_voice': '4',  # Nova (Female)
            'speaker_2_voice': '3'   # Onyx (Male)
        }
        
        success = generator.generate_audio_from_script(
            script=custom_script,
            output_path=output_path,
            language="en",
            voice_selections=voice_selections
        )
        
        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ Custom script audio generated successfully!")
            print(f"Output file: {output_path}")
            print(f"File size: {file_size} bytes")
            return True
        else:
            print("❌ Failed to generate custom script audio")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_voice_parsing():
    """Test script parsing for speakers"""
    print("\n🎭 Testing Voice Parsing...")
    
    test_script = """Speaker 1: Hello, this is speaker one.
Speaker 2: And this is speaker two responding.
Speaker 1: Back to speaker one for more content.
Speaker 3: Now speaker three joins the conversation."""
    
    try:
        from ai_services import VoiceGenerator
        
        generator = VoiceGenerator()
        segments = generator.parse_script(test_script)
        
        print(f"✅ Parsed {len(segments)} segments:")
        for speaker_num, dialogue in segments:
            print(f"  Speaker {speaker_num}: {dialogue[:50]}...")
        
        return len(segments) > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🎙️ Full Workflow Test Suite")
    print("=" * 50)
    
    # Test 1: Voice parsing
    test1 = test_voice_parsing()
    
    # Test 2: Script generation
    script = test_script_generation()
    test2 = script is not None
    
    # Test 3: Audio generation from generated script
    test3 = False
    if script:
        test3 = test_audio_generation(script)
    
    # Test 4: Custom script audio
    test4 = test_custom_script()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Voice Parsing: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"  Script Generation: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"  Generated Script Audio: {'✅ PASS' if test3 else '❌ FAIL'}")
    print(f"  Custom Script Audio: {'✅ PASS' if test4 else '❌ FAIL'}")
    
    if all([test1, test2, test3, test4]):
        print("\n🎉 ALL TESTS PASSED! Full workflow is working!")
        print("\n🎯 Generated Files:")
        if os.path.exists("test_full_workflow_audio.mp3"):
            print(f"  - test_full_workflow_audio.mp3 ({os.path.getsize('test_full_workflow_audio.mp3')} bytes)")
        if os.path.exists("test_custom_script_audio.mp3"):
            print(f"  - test_custom_script_audio.mp3 ({os.path.getsize('test_custom_script_audio.mp3')} bytes)")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        
    print("\n🚀 The AI Podcast Generator is ready for use!")
    print("Visit: http://localhost:5000/demo_login")
