{% extends "base.html" %}

{% block title %}Admin Dashboard - AI Podcast Generator{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-6 fw-bold">
                        <i class="fas fa-shield-alt text-danger"></i> Admin Dashboard
                    </h1>
                    <p class="text-muted">System administration and user management</p>
                </div>
                <div>
                    <a href="{{ url_for('create_demo_users') }}" class="btn btn-warning btn-custom">
                        <i class="fas fa-user-plus"></i> Create Demo Users
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="row mb-5">
        <div class="col-md-3 mb-3">
            <div class="card card-custom text-center p-3 bg-primary text-white">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3 class="fw-bold">{{ stats.total_users }}</h3>
                <p class="mb-0">Total Users</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card card-custom text-center p-3 bg-success text-white">
                <i class="fas fa-project-diagram fa-2x mb-2"></i>
                <h3 class="fw-bold">{{ stats.total_projects }}</h3>
                <p class="mb-0">Total Projects</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card card-custom text-center p-3 bg-info text-white">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h3 class="fw-bold">{{ stats.completed_projects }}</h3>
                <p class="mb-0">Completed</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card card-custom text-center p-3 bg-warning text-white">
                <i class="fas fa-user-tag fa-2x mb-2"></i>
                <h3 class="fw-bold">{{ stats.demo_users }}</h3>
                <p class="mb-0">Demo Users</p>
            </div>
        </div>
    </div>

    <!-- Demo Credentials Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-key"></i> Demo Credentials
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold text-primary">Admin Account</h6>
                            <div class="bg-light p-3 rounded mb-3">
                                <p class="mb-1"><strong>Username:</strong> <code>admin</code></p>
                                <p class="mb-1"><strong>Password:</strong> <code>admin123</code></p>
                                <p class="mb-0"><strong>Access:</strong> Full admin privileges</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold text-success">Demo User Accounts</h6>
                            <div class="bg-light p-3 rounded mb-3">
                                <p class="mb-1"><strong>Username:</strong> <code>demo_user</code></p>
                                <p class="mb-1"><strong>Password:</strong> <code>demo123</code></p>
                                <p class="mb-1"><strong>Username:</strong> <code>content_creator</code></p>
                                <p class="mb-1"><strong>Password:</strong> <code>creator123</code></p>
                                <p class="mb-0"><strong>Access:</strong> Standard user features</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Quick Demo Login:</strong> 
                                <a href="{{ url_for('demo_login') }}" class="btn btn-sm btn-primary ms-2">
                                    <i class="fas fa-sign-in-alt"></i> Login as Demo User
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Management -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users"></i> User Management
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Type</th>
                                    <th>Projects</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.id }}</td>
                                    <td>
                                        {{ user.username }}
                                        {% if user.is_admin %}
                                            <span class="badge bg-danger ms-1">Admin</span>
                                        {% endif %}
                                        {% if user.is_demo %}
                                            <span class="badge bg-warning ms-1">Demo</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        {% if user.is_admin %}
                                            <span class="text-danger fw-bold">Administrator</span>
                                        {% elif user.is_demo %}
                                            <span class="text-warning fw-bold">Demo User</span>
                                        {% else %}
                                            <span class="text-success fw-bold">Regular User</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ user.projects|length }}</span>
                                    </td>
                                    <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewUserProjects({{ user.id }})">
                                            <i class="fas fa-eye"></i> View
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Projects -->
    <div class="row">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-project-diagram"></i> Recent Projects
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Project</th>
                                    <th>User</th>
                                    <th>Keyword</th>
                                    <th>Language</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for project in projects[:10] %}
                                <tr>
                                    <td>{{ project.title }}</td>
                                    <td>{{ project.user.username }}</td>
                                    <td><code>{{ project.keyword }}</code></td>
                                    <td>{{ project.language.upper() }}</td>
                                    <td>
                                        {% if project.status == 'completed' %}
                                            <span class="badge bg-success">Completed</span>
                                        {% elif project.status == 'processing' or project.status == 'generating_video' %}
                                            <span class="badge bg-warning">Processing</span>
                                        {% elif project.status == 'error' %}
                                            <span class="badge bg-danger">Error</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ project.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ project.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <a href="{{ url_for('project_detail', project_id=project.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Dashboard -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary btn-custom">
                <i class="fas fa-arrow-left"></i> Back to User Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function viewUserProjects(userId) {
    // In a real application, this would show user's projects
    alert('Feature coming soon: View user projects for user ID ' + userId);
}
</script>
{% endblock %}
