{% extends "base.html" %}

{% block title %}Login - AI Podcast Generator{% endblock %}

{% block content %}
<div class="gradient-bg">
    <div class="container">
        <div class="row min-vh-100 align-items-center justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card card-custom">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-microphone-alt fa-3x text-primary mb-3"></i>
                            <h2 class="fw-bold">Welcome Back</h2>
                            <p class="text-muted">Sign in to your account</p>
                        </div>

                        <form method="POST">
                            {{ form.hidden_tag() }}

                            <div class="mb-3">
                                <label for="{{ form.username.id }}" class="form-label">
                                    <i class="fas fa-user"></i> Username
                                </label>
                                {{ form.username(class="form-control", placeholder="Enter your username") }}
                                {% if form.username.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.username.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-4">
                                <label for="{{ form.password.id }}" class="form-label">
                                    <i class="fas fa-lock"></i> Password
                                </label>
                                {{ form.password(class="form-control", placeholder="Enter your password") }}
                                {% if form.password.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.password.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <button type="submit" class="btn btn-primary btn-custom w-100 mb-3">
                                <i class="fas fa-sign-in-alt"></i> Sign In
                            </button>
                        </form>

                        <div class="text-center">
                            <div class="mb-3">
                                <p class="text-muted mb-2">Quick Demo Access:</p>
                                <a href="{{ url_for('demo_login') }}" class="btn btn-warning btn-sm me-2">
                                    <i class="fas fa-user"></i> Demo User
                                </a>
                                <small class="text-muted">No password needed</small>
                            </div>

                            <hr class="my-3">

                            <p class="text-muted">
                                Don't have an account?
                                <a href="{{ url_for('register') }}" class="text-primary text-decoration-none">
                                    Sign up here
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
