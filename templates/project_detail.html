{% extends "base.html" %}

{% block title %}{{ project.title }} - AI Podcast Generator{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Project Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 fw-bold">{{ project.title }}</h1>
                    <p class="text-muted">
                        {% if project.content_type == 'generate' %}
                            <span class="badge bg-primary me-2">AI Generated</span>
                            <i class="fas fa-tag"></i> {{ project.keyword }} •
                        {% else %}
                            <span class="badge bg-success me-2">Custom Script</span>
                        {% endif %}
                        <i class="fas fa-users"></i> {{ project.num_speakers }} speaker(s) •
                        <i class="fas fa-palette"></i> {{ project.tone|title }}
                        {% if project.user_media_file %}
                        • <i class="fas fa-{% if project.user_media_type == 'image' %}image{% else %}video{% endif %} text-info"></i> Custom {{ project.user_media_type|title }}
                        {% endif %}
                    </p>
                </div>
                <div>
                    {% if project.status == 'completed' %}
                        <span class="badge bg-success status-badge fs-6">
                            <i class="fas fa-check"></i> Completed
                        </span>
                    {% elif project.status == 'processing' or project.status == 'generating_video' %}
                        <span class="badge bg-warning status-badge fs-6">
                            <i class="fas fa-spinner fa-spin"></i> Processing
                        </span>
                    {% elif project.status == 'content_ready' %}
                        <span class="badge bg-info status-badge fs-6">
                            <i class="fas fa-video"></i> Ready for Video
                        </span>
                    {% elif project.status == 'error' %}
                        <span class="badge bg-danger status-badge fs-6">
                            <i class="fas fa-exclamation-triangle"></i> Error
                        </span>
                    {% else %}
                        <span class="badge bg-secondary status-badge fs-6">
                            <i class="fas fa-clock"></i> Created
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-body">
                    <h5 class="mb-3">
                        <i class="fas fa-tasks"></i> Progress
                    </h5>
                    <div class="progress progress-custom mb-3">
                        {% if project.status == 'created' %}
                            <div class="progress-bar" style="width: 10%"></div>
                        {% elif project.status == 'processing' %}
                            <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 50%"></div>
                        {% elif project.status == 'content_ready' %}
                            <div class="progress-bar bg-info" style="width: 75%"></div>
                        {% elif project.status == 'generating_video' %}
                            <div class="progress-bar bg-warning progress-bar-striped progress-bar-animated" style="width: 90%"></div>
                        {% elif project.status == 'completed' %}
                            <div class="progress-bar bg-success" style="width: 100%"></div>
                        {% elif project.status == 'error' %}
                            <div class="progress-bar bg-danger" style="width: 100%"></div>
                        {% endif %}
                    </div>
                    <div class="row text-center">
                        <div class="col">
                            <small class="text-muted">
                                <i class="fas fa-edit"></i><br>Script
                            </small>
                        </div>
                        <div class="col">
                            <small class="text-muted">
                                <i class="fas fa-microphone"></i><br>Audio
                            </small>
                        </div>
                        <div class="col">
                            <small class="text-muted">
                                <i class="fas fa-video"></i><br>Video
                            </small>
                        </div>
                        <div class="col">
                            <small class="text-muted">
                                <i class="fas fa-download"></i><br>Final
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Messages -->
    <div id="statusMessages"></div>

    <!-- Content Sections -->
    <div class="row">
        <!-- Script Section -->
        <div class="col-lg-6 mb-4">
            <div class="card card-custom h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt"></i> Generated Script
                    </h5>
                </div>
                <div class="card-body">
                    {% if project.script %}
                        <div class="script-content" style="max-height: 400px; overflow-y: auto;">
                            <pre class="text-wrap">{{ project.script }}</pre>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm" onclick="copyToClipboard('script')">
                                <i class="fas fa-copy"></i> Copy Script
                            </button>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            {% if project.status == 'processing' %}
                                <div class="loading-spinner mb-3"></div>
                                <p>Generating script...</p>
                            {% else %}
                                <i class="fas fa-hourglass-half fa-2x mb-3"></i>
                                <p>Script will appear here once generated</p>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Postcard Section -->
        <div class="col-lg-6 mb-4">
            <div class="card card-custom h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-postcard"></i> Postcard Text
                    </h5>
                </div>
                <div class="card-body">
                    {% if project.postcard_text %}
                        <div class="postcard-content">
                            <p class="lead">{{ project.postcard_text }}</p>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-success btn-sm" onclick="copyToClipboard('postcard')">
                                <i class="fas fa-copy"></i> Copy Text
                            </button>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            {% if project.status == 'processing' %}
                                <div class="loading-spinner mb-3"></div>
                                <p>Generating postcard text...</p>
                            {% else %}
                                <i class="fas fa-hourglass-half fa-2x mb-3"></i>
                                <p>Postcard text will appear here once generated</p>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Audio Section -->
    {% if project.audio_file %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-headphones"></i> Generated Audio
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="fas fa-music fa-3x text-warning mb-3"></i>
                            <h4>Your podcast audio is ready!</h4>
                            <p class="text-muted">Listen to your AI-generated podcast audio below.</p>
                        </div>

                        <!-- Audio Player -->
                        <div class="audio-player-container mb-4">
                            <audio controls class="w-100" style="max-width: 600px;">
                                <source src="{{ url_for('serve_audio', project_id=project.id) }}" type="audio/mpeg">
                                Your browser does not support the audio element.
                            </audio>
                        </div>

                        <!-- Audio Actions -->
                        <div class="d-flex gap-3 justify-content-center">
                            <a href="{{ url_for('serve_audio', project_id=project.id) }}"
                               download="{{ project.title }}.mp3"
                               class="btn btn-warning btn-lg btn-custom">
                                <i class="fas fa-download"></i> Download Audio
                            </a>
                            <button class="btn btn-outline-primary btn-lg btn-custom" onclick="proceedToVideo()">
                                <i class="fas fa-video"></i> Create Video
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Video Selection Section (Hidden by default, shown when user clicks "Create Video") -->
    <div class="row mb-4" id="videoSelectionSection" style="display: none;">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-video"></i> Video Selection
                    </h5>
                </div>
                <div class="card-body">
                    <div id="videoSelection">
                        <div class="text-center py-4">
                            <div class="loading-spinner mb-3"></div>
                            <p>Loading video options...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Final Video Section -->
    {% if project.status == 'completed' and project.final_video %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-play-circle"></i> Final Video
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h4>Your podcast is ready!</h4>
                        <p class="text-muted">Your AI-generated podcast video has been created successfully.</p>
                    </div>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="{{ url_for('download_video', project_id=project.id) }}"
                           class="btn btn-success btn-lg btn-custom">
                            <i class="fas fa-download"></i> Download Video
                        </a>
                        <button class="btn btn-outline-primary btn-lg btn-custom" onclick="shareProject()">
                            <i class="fas fa-share"></i> Share
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Actions -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex gap-3 justify-content-between">
                <div class="d-flex gap-3">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary btn-custom">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                    {% if project.status == 'error' %}
                        <button class="btn btn-warning btn-custom" onclick="retryProject()">
                            <i class="fas fa-redo"></i> Retry
                        </button>
                    {% endif %}
                </div>
                <button class="btn btn-danger btn-custom" onclick="deleteProject('{{ project.id }}', '{{ project.title }}')">
                    <i class="fas fa-trash"></i> Delete Project
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden elements for copying -->
<textarea id="scriptText" style="position: absolute; left: -9999px;">{{ project.script or '' }}</textarea>
<textarea id="postcardText" style="position: absolute; left: -9999px;">{{ project.postcard_text or '' }}</textarea>
{% endblock %}

{% block scripts %}
<script>
// Initialize Socket.IO
const socket = io();
const projectId = '{{ project.id }}';
let selectedVideoUrl = null;

// Connect to project room
socket.emit('join_project', {project_id: projectId});

// Handle status updates
socket.on('status_update', function(data) {
    if (data.project_id === projectId) {
        showStatusMessage(data.message, data.status === 'error' ? 'danger' : 'info');
        if (data.status === 'error') {
            setTimeout(() => location.reload(), 2000);
        }
    }
});

// Handle content generation completion
socket.on('content_generated', function(data) {
    if (data.project_id === projectId) {
        showStatusMessage('Content generated successfully!', 'success');
        setTimeout(() => location.reload(), 1000);
    }
});

// Handle video completion
socket.on('video_completed', function(data) {
    if (data.project_id === projectId) {
        showStatusMessage('Video generated successfully!', 'success');
        setTimeout(() => location.reload(), 1000);
    }
});

// Video options will be loaded when user clicks "Create Video" button

function showStatusMessage(message, type) {
    const alertClass = type === 'error' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' : 'alert-info';

    const html = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.getElementById('statusMessages').innerHTML = html;
}

function loadVideoOptions() {
    // First show generated background options
    const generatedVideos = [
        {
            id: 'abstract_bg',
            preview: 'https://via.placeholder.com/300x200/2c3e50/ffffff?text=Abstract',
            download_url: 'abstract_bg',
            title: 'Abstract Background',
            description: 'Generated geometric patterns'
        },
        {
            id: 'gradient_bg',
            preview: 'https://via.placeholder.com/300x200/3498db/ffffff?text=Gradient',
            download_url: 'gradient_bg',
            title: 'Gradient Background',
            description: 'Generated color transitions'
        },
        {
            id: 'minimal_bg',
            preview: 'https://via.placeholder.com/300x200/34495e/ffffff?text=Minimal',
            download_url: 'minimal_bg',
            title: 'Minimal Background',
            description: 'Generated clean design'
        }
    ];

    // Search for real Pexels videos based on project keyword
    const keyword = '{{ project.keyword or "technology" }}';

    fetch(`/api/search_videos?keyword=${encodeURIComponent(keyword)}&per_page=6`)
        .then(response => response.json())
        .then(data => {
            let allVideos = [...generatedVideos];

            if (data.status === 'success' && data.videos.length > 0) {
                // Add real Pexels videos
                const pexelsVideos = data.videos.map(video => ({
                    id: `pexels_${video.id}`,
                    preview: video.preview,
                    download_url: video.download_url,
                    title: video.title,
                    description: video.description
                }));

                allVideos = [...allVideos, ...pexelsVideos];
                showStatusMessage(`Found ${data.videos.length} videos from Pexels!`, 'success');
            } else {
                showStatusMessage('Using generated backgrounds (Pexels search failed)', 'warning');
            }

            displayVideoOptions(allVideos);
        })
        .catch(error => {
            console.error('Error loading Pexels videos:', error);
            showStatusMessage('Using generated backgrounds only', 'warning');
            displayVideoOptions(generatedVideos);
        });
}

function displayVideoOptions(videos) {
    const videoGrid = videos.map(video => `
        <div class="col-md-4 mb-3">
            <div class="card video-card h-100" onclick="selectVideo('${video.download_url}', this)" style="cursor: pointer;">
                <img src="${video.preview}" class="card-img-top" alt="${video.title}" style="height: 150px; object-fit: cover;">
                <div class="card-body text-center d-flex flex-column">
                    <h6 class="card-title">${video.title}</h6>
                    <p class="card-text text-muted small flex-grow-1">${video.description}</p>
                    <button class="btn btn-outline-primary btn-sm mt-auto">
                        <i class="fas fa-check"></i> Select
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    const html = `
        <h6 class="mb-3">Choose a video for your podcast:</h6>
        <div class="row video-grid">
            ${videoGrid}
        </div>
        <div class="mt-3">
            <button id="generateVideoBtn" class="btn btn-primary btn-custom" onclick="generateFinalVideo()" disabled>
                <i class="fas fa-video"></i> Generate Final Video
            </button>
        </div>
    `;

    document.getElementById('videoSelection').innerHTML = html;
}

function selectVideo(videoUrl, element) {
    // Remove selection from other videos
    document.querySelectorAll('.video-card').forEach(card => {
        card.classList.remove('selected');
    });

    // Select this video
    element.classList.add('selected');
    selectedVideoUrl = videoUrl;

    // Enable generate button
    document.getElementById('generateVideoBtn').disabled = false;
}

function generateFinalVideo() {
    if (!selectedVideoUrl) {
        alert('Please select a video first');
        return;
    }

    const btn = document.getElementById('generateVideoBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    btn.disabled = true;

    fetch('/api/generate_video', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            project_id: projectId,
            video_url: selectedVideoUrl
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'completed') {
            showStatusMessage('Video generated successfully!', 'success');
            btn.innerHTML = '<i class="fas fa-check"></i> Video Ready';
            btn.disabled = true;

            // Reload the page to show the final video section
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else if (data.status === 'error') {
            showStatusMessage(`Error generating video: ${data.message || 'Unknown error'}`, 'error');
            btn.innerHTML = '<i class="fas fa-video"></i> Generate Final Video';
            btn.disabled = false;
        } else {
            showStatusMessage('Error starting video generation', 'error');
            btn.innerHTML = '<i class="fas fa-video"></i> Generate Final Video';
            btn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showStatusMessage('Error starting video generation', 'error');
        btn.innerHTML = '<i class="fas fa-video"></i> Generate Final Video';
        btn.disabled = false;
    });
}

function copyToClipboard(type) {
    const textArea = document.getElementById(type + 'Text');
    textArea.select();
    document.execCommand('copy');

    showStatusMessage(`${type.charAt(0).toUpperCase() + type.slice(1)} copied to clipboard!`, 'success');
}

function shareProject() {
    const url = window.location.href;
    if (navigator.share) {
        navigator.share({
            title: '{{ project.title }}',
            text: 'Check out this AI-generated podcast!',
            url: url
        });
    } else {
        // Fallback to copying URL
        navigator.clipboard.writeText(url).then(() => {
            showStatusMessage('Project URL copied to clipboard!', 'success');
        });
    }
}

function retryProject() {
    if (confirm('Are you sure you want to retry generating this project?')) {
        // In a real app, this would trigger a retry
        location.reload();
    }
}

function proceedToVideo() {
    // Show the video selection section
    const videoSection = document.getElementById('videoSelectionSection');
    if (videoSection) {
        videoSection.style.display = 'block';
        videoSection.scrollIntoView({ behavior: 'smooth' });

        // Load video options if not already loaded
        if (!selectedVideoUrl) {
            loadVideoOptions();
        }
    }
}

function deleteProject(projectId, projectTitle) {
    if (confirm(`Are you sure you want to delete "${projectTitle}"?\n\nThis action cannot be undone and will delete all associated files.`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/delete/${projectId}`;

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-refresh status for processing projects
{% if project.status in ['processing', 'generating_video'] %}
setInterval(() => {
    fetch(`/api/project/${projectId}/status`)
        .then(response => response.json())
        .then(data => {
            if (data.status !== '{{ project.status }}') {
                location.reload();
            }
        })
        .catch(error => console.error('Error checking status:', error));
}, 5000); // Check every 5 seconds
{% endif %}
</script>
{% endblock %}
