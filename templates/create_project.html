{% extends "base.html" %}

{% block title %}Create Project - AI Podcast Generator{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card card-custom">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-magic fa-3x text-primary mb-3"></i>
                        <h2 class="fw-bold">Create New Podcast</h2>
                        <p class="text-muted">Let AI transform your idea into an engaging podcast</p>
                    </div>

                    <form method="POST" id="projectForm" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}

                        <!-- Content Type Selection -->
                        <div class="row">
                            <div class="col-12 mb-4">
                                <label for="{{ form.content_type.id }}" class="form-label fw-bold">
                                    <i class="fas fa-magic text-primary"></i> How would you like to create your podcast?
                                </label>
                                {{ form.content_type(class="form-select form-select-lg", onchange="toggleContentType()") }}
                                <div class="form-text">
                                    <small>Choose between AI generation or providing your own script</small>
                                </div>
                            </div>
                        </div>

                        <!-- AI Generation Section -->
                        <div id="ai-generation-section">
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <label for="{{ form.keyword.id }}" class="form-label fw-bold">
                                        <i class="fas fa-lightbulb text-warning"></i> What's your topic?
                                    </label>
                                    {{ form.keyword(class="form-control form-control-lg", placeholder="e.g., Artificial Intelligence, Climate Change, Space Exploration") }}
                                    {% if form.keyword.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.keyword.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">
                                        <small>Enter any topic you'd like to create a podcast about</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Custom Script Section -->
                        <div id="custom-script-section" style="display: none;">
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <label for="{{ form.custom_script.id }}" class="form-label fw-bold">
                                        <i class="fas fa-edit text-success"></i> Paste Your Podcast Script
                                    </label>
                                    {{ form.custom_script(class="form-control", rows="8", placeholder="Paste your podcast script here...\n\nFor multi-speaker podcasts, format like:\nSpeaker 1: Hello and welcome to our show!\nSpeaker 2: Thanks for having me!\nSpeaker 1: Today we're discussing...") }}
                                    {% if form.custom_script.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.custom_script.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">
                                        <small>
                                            <strong>Multi-speaker format:</strong> Use "Speaker 1:", "Speaker 2:", etc. for different voices<br>
                                            <strong>Single speaker:</strong> Just paste your text directly
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label for="{{ form.language.id }}" class="form-label fw-bold">
                                    <i class="fas fa-globe text-info"></i> Language
                                </label>
                                {{ form.language(class="form-select") }}
                                {% if form.language.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.language.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-4">
                                <label for="{{ form.num_speakers.id }}" class="form-label fw-bold">
                                    <i class="fas fa-users text-success"></i> Number of Speakers
                                </label>
                                {{ form.num_speakers(class="form-select") }}
                                {% if form.num_speakers.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.num_speakers.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <small>More speakers create natural conversations</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12 mb-4">
                                <label for="{{ form.tone.id }}" class="form-label fw-bold">
                                    <i class="fas fa-palette text-danger"></i> Tone & Style
                                </label>
                                {{ form.tone(class="form-select") }}
                                {% if form.tone.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.tone.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <small>Choose the mood and style for your podcast</small>
                                </div>
                            </div>
                        </div>

                        <!-- Video Options -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card card-custom">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-magic"></i> Video Options
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Auto Video Creation -->
                                        <div class="form-check form-switch mb-3">
                                            {{ form.auto_create_video(class="form-check-input") }}
                                            <label class="form-check-label" for="{{ form.auto_create_video.id }}">
                                                <strong>{{ form.auto_create_video.label.text }}</strong>
                                            </label>
                                            <div class="form-text">
                                                <small>
                                                    <i class="fas fa-info-circle text-info"></i>
                                                    When enabled, a video will be automatically created with a gradient background after audio generation completes.
                                                    You can still create additional videos with different backgrounds later.
                                                </small>
                                            </div>
                                        </div>

                                        <!-- Subtitle Option -->
                                        <div class="form-check form-switch">
                                            {{ form.include_subtitles(class="form-check-input") }}
                                            <label class="form-check-label" for="{{ form.include_subtitles.id }}">
                                                <strong>{{ form.include_subtitles.label.text }}</strong>
                                            </label>
                                            <div class="form-text">
                                                <small>
                                                    <i class="fas fa-closed-captioning text-primary"></i>
                                                    Add synchronized subtitles/captions to your video. Subtitles will show the dialogue with speaker labels and proper timing.
                                                    Great for accessibility and social media sharing!
                                                </small>
                                            </div>
                                        </div>

                                        <!-- Caption Format Options -->
                                        <div id="subtitle_options" class="mt-3" style="margin-left: 20px; display: none;">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="subtitle_format" class="form-label fw-bold">
                                                        <i class="fas fa-cog text-info"></i> Caption Format
                                                    </label>
                                                    <select class="form-select" id="subtitle_format" name="subtitle_format">
                                                        <option value="auto">🤖 Auto-detect from audio (Replicate API)</option>
                                                        <option value="script" selected>📝 Generate from script text</option>
                                                        <option value="large_text">📱 Large text overlay (social media)</option>
                                                        <option value="minimal">✨ Minimal style</option>
                                                    </select>
                                                    <div class="form-text">
                                                        <small>Choose how captions should be generated</small>
                                                    </div>
                                                </div>

                                                <div class="col-md-6 mb-3">
                                                    <label for="subtitle_style" class="form-label fw-bold">
                                                        <i class="fas fa-palette text-warning"></i> Caption Style
                                                    </label>
                                                    <select class="form-select" id="subtitle_style" name="subtitle_style">
                                                        <option value="white_black_outline" selected>⚪ White text with black outline</option>
                                                        <option value="black_white_outline">⚫ Black text with white outline</option>
                                                        <option value="yellow_black_outline">🟡 Yellow text with black outline</option>
                                                        <option value="white_shadow">💫 White text with shadow</option>
                                                        <option value="bold_white">💪 Bold white text</option>
                                                    </select>
                                                    <div class="form-text">
                                                        <small>Choose the visual style for your captions</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="alert alert-info">
                                                <h6><i class="fas fa-info-circle"></i> Caption Preview:</h6>
                                                <div id="caption_preview" class="text-center p-3" style="background: #000; border-radius: 8px; position: relative;">
                                                    <div style="color: white; text-shadow: 2px 2px 4px black; font-size: 18px; font-weight: bold;">
                                                        Speaker 1: Bonjour et bienvenue dans notre podcast!
                                                    </div>
                                                </div>
                                                <small class="text-muted mt-2 d-block">
                                                    <i class="fas fa-lightbulb"></i> This is how your captions will appear on the video
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Media Upload Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card card-custom">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-upload"></i> Upload Your Own Media (Optional)
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-12 mb-3">
                                                <label for="{{ form.user_media.id }}" class="form-label fw-bold">
                                                    <i class="fas fa-image"></i> Upload Image or Video
                                                </label>
                                                {{ form.user_media(class="form-control", accept=".jpg,.jpeg,.png,.gif,.mp4,.mov,.avi,.webm", onchange="previewMedia(this)") }}
                                                {% if form.user_media.errors %}
                                                    <div class="text-danger small mt-1">
                                                        {% for error in form.user_media.errors %}
                                                            <div>{{ error }}</div>
                                                        {% endfor %}
                                                    </div>
                                                {% endif %}
                                                <div class="form-text">
                                                    <small>
                                                        <strong>Supported formats:</strong><br>
                                                        <i class="fas fa-image text-primary"></i> Images: JPG, JPEG, PNG, GIF (max 10MB)<br>
                                                        <i class="fas fa-video text-success"></i> Videos: MP4, MOV, AVI, WEBM (max 50MB)<br>
                                                        <em>If no media is uploaded, the system will automatically find relevant stock footage.</em>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Media Preview -->
                                        <div id="mediaPreview" class="mt-3" style="display: none;">
                                            <div class="alert alert-info">
                                                <h6><i class="fas fa-eye"></i> Preview:</h6>
                                                <div id="previewContainer" class="text-center">
                                                    <!-- Preview will be inserted here -->
                                                </div>
                                                <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="clearMedia()">
                                                    <i class="fas fa-trash"></i> Remove Media
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Voice Selection Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card card-custom">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-microphone"></i> Voice Selection
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i>
                                            <strong>Voice Selection:</strong> Choose different voices for each speaker to create natural conversations.
                                            Voices will update automatically based on your selected language and TTS provider.
                                        </div>

                                        <div class="row" id="voiceSelectionContainer">
                                            <!-- Voice selections will be populated dynamically -->
                                            <div class="col-md-4 mb-3" id="speaker1VoiceContainer">
                                                <label for="{{ form.speaker_1_voice.id }}" class="form-label fw-bold">
                                                    <i class="fas fa-user"></i> Speaker 1 Voice
                                                </label>
                                                {{ form.speaker_1_voice(class="form-select", id="speaker1Voice") }}
                                                <div class="form-text">Primary speaker voice</div>
                                            </div>

                                            <div class="col-md-4 mb-3" id="speaker2VoiceContainer" style="display: none;">
                                                <label for="{{ form.speaker_2_voice.id }}" class="form-label fw-bold">
                                                    <i class="fas fa-user-friends"></i> Speaker 2 Voice
                                                </label>
                                                {{ form.speaker_2_voice(class="form-select", id="speaker2Voice") }}
                                                <div class="form-text">Second speaker voice</div>
                                            </div>

                                            <div class="col-md-4 mb-3" id="speaker3VoiceContainer" style="display: none;">
                                                <label for="{{ form.speaker_3_voice.id }}" class="form-label fw-bold">
                                                    <i class="fas fa-users"></i> Speaker 3 Voice
                                                </label>
                                                {{ form.speaker_3_voice(class="form-select", id="speaker3Voice") }}
                                                <div class="form-text">Third speaker voice</div>
                                            </div>
                                        </div>

                                        <!-- Voice Preview -->
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <div class="alert alert-light">
                                                    <h6><i class="fas fa-volume-up"></i> Voice Preview:</h6>
                                                    <div id="voicePreview">
                                                        <small class="text-muted">Select voices above to see preview</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Section -->
                        <div class="alert alert-light border mb-4">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-eye text-primary"></i> What you'll get:
                            </h6>
                            <div id="ai-preview">
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                AI-generated script with natural dialogue
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Professional voice synthesis
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Curated stock video options
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Final MP4 video with optional subtitles
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div id="custom-preview" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Your custom script with professional audio
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Multi-voice support for dialogue
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Smart video selection based on content
                                            </li>
                                            <li class="mb-2">
                                                <i class="fas fa-check text-success me-2"></i>
                                                Final MP4 video with optional subtitles
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-3">
                            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary btn-custom">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary btn-custom flex-fill" id="submitBtn">
                                <i class="fas fa-rocket"></i> Create Podcast
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tips Section -->
    <div class="row mt-5">
        <div class="col-lg-8 mx-auto">
            <div class="card card-custom bg-light">
                <div class="card-body p-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-lightbulb text-warning"></i> Pro Tips
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-star text-warning me-2"></i>
                                    Use specific, interesting keywords for better content
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-star text-warning me-2"></i>
                                    Multiple speakers create more engaging conversations
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-star text-warning me-2"></i>
                                    Educational tone works great for complex topics
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-star text-warning me-2"></i>
                                    Processing typically takes 2-5 minutes
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleContentType() {
    const contentType = document.getElementById('{{ form.content_type.id }}').value;
    const aiSection = document.getElementById('ai-generation-section');
    const customSection = document.getElementById('custom-script-section');
    const aiPreview = document.getElementById('ai-preview');
    const customPreview = document.getElementById('custom-preview');
    const submitBtn = document.getElementById('submitBtn');

    if (contentType === 'generate') {
        aiSection.style.display = 'block';
        customSection.style.display = 'none';
        aiPreview.style.display = 'block';
        customPreview.style.display = 'none';
        submitBtn.innerHTML = '<i class="fas fa-rocket"></i> Generate Podcast';
    } else {
        aiSection.style.display = 'none';
        customSection.style.display = 'block';
        aiPreview.style.display = 'none';
        customPreview.style.display = 'block';
        submitBtn.innerHTML = '<i class="fas fa-microphone"></i> Create Podcast';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleContentType();
    updateVoiceSelections();
    updateSpeakerVoiceVisibility();
    toggleSubtitleOptions();
    updateCaptionPreview();
});

document.getElementById('projectForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const contentType = document.getElementById('{{ form.content_type.id }}').value;

    if (contentType === 'generate') {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    } else {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    }
    submitBtn.disabled = true;
});

// Real-time character count for custom script
document.getElementById('{{ form.custom_script.id }}').addEventListener('input', function() {
    const textarea = this;
    const charCount = textarea.value.length;
    const maxLength = 5000;

    // Find or create character counter
    let counter = document.getElementById('char-counter');
    if (!counter) {
        counter = document.createElement('div');
        counter.id = 'char-counter';
        counter.className = 'form-text text-end';
        textarea.parentNode.appendChild(counter);
    }

    counter.innerHTML = `<small>${charCount}/${maxLength} characters</small>`;

    if (charCount > maxLength * 0.9) {
        counter.className = 'form-text text-end text-warning';
    } else if (charCount > maxLength) {
        counter.className = 'form-text text-end text-danger';
    } else {
        counter.className = 'form-text text-end text-muted';
    }
});

// Auto-resize textarea
document.getElementById('{{ form.custom_script.id }}').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = (this.scrollHeight) + 'px';
});

// Media preview functionality
function previewMedia(input) {
    const file = input.files[0];
    const preview = document.getElementById('mediaPreview');
    const container = document.getElementById('previewContainer');

    if (file) {
        const fileSize = file.size / 1024 / 1024; // Convert to MB
        const fileName = file.name;
        const fileType = file.type;

        // Check file size limits
        if (fileType.startsWith('image/') && fileSize > 10) {
            alert('Image file size must be less than 10MB');
            clearMedia();
            return;
        }
        if (fileType.startsWith('video/') && fileSize > 50) {
            alert('Video file size must be less than 50MB');
            clearMedia();
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            let previewHTML = '';

            if (fileType.startsWith('image/')) {
                previewHTML = `
                    <div class="mb-2">
                        <img src="${e.target.result}" alt="Preview" style="max-width: 300px; max-height: 200px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    </div>
                    <div class="text-muted small">
                        <i class="fas fa-image text-primary"></i> ${fileName} (${fileSize.toFixed(2)} MB)
                    </div>
                `;
            } else if (fileType.startsWith('video/')) {
                previewHTML = `
                    <div class="mb-2">
                        <video controls style="max-width: 300px; max-height: 200px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <source src="${e.target.result}" type="${fileType}">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    <div class="text-muted small">
                        <i class="fas fa-video text-success"></i> ${fileName} (${fileSize.toFixed(2)} MB)
                    </div>
                `;
            }

            container.innerHTML = previewHTML;
            preview.style.display = 'block';
        };

        reader.readAsDataURL(file);
    } else {
        clearMedia();
    }
}

function clearMedia() {
    const input = document.getElementById('{{ form.user_media.id }}');
    const preview = document.getElementById('mediaPreview');
    const container = document.getElementById('previewContainer');

    input.value = '';
    container.innerHTML = '';
    preview.style.display = 'none';
}

// File drag and drop functionality
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('{{ form.user_media.id }}');
    const dropZone = fileInput.closest('.card-body');

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop zone when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });

    // Handle dropped files
    dropZone.addEventListener('drop', handleDrop, false);

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight(e) {
        dropZone.classList.add('border-primary', 'bg-light');
    }

    function unhighlight(e) {
        dropZone.classList.remove('border-primary', 'bg-light');
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            fileInput.files = files;
            previewMedia(fileInput);
        }
    }
});

// Voice selection functions
function updateVoiceSelections() {
    const language = document.getElementById('{{ form.language.id }}').value;

    fetch(`/api/voices/${language}`)
        .then(response => response.json())
        .then(data => {
            if (data.voices) {
                updateVoiceDropdowns(data.voices);
                updateVoicePreview();
            }
        })
        .catch(error => {
            console.error('Error fetching voices:', error);
        });
}

function updateVoiceDropdowns(voices) {
    const speaker1Voice = document.getElementById('speaker1Voice');
    const speaker2Voice = document.getElementById('speaker2Voice');
    const speaker3Voice = document.getElementById('speaker3Voice');

    // Clear existing options
    [speaker1Voice, speaker2Voice, speaker3Voice].forEach(select => {
        select.innerHTML = '';
        voices.forEach(voice => {
            const option = document.createElement('option');
            option.value = voice.value;
            option.textContent = voice.label;
            select.appendChild(option);
        });
    });

    // Set default selections (different voices for each speaker)
    if (voices.length > 0) {
        speaker1Voice.value = voices[0].value;
        if (voices.length > 1) speaker2Voice.value = voices[1].value;
        if (voices.length > 2) speaker3Voice.value = voices[2].value;
        else if (voices.length > 1) speaker3Voice.value = voices[1].value;
    }
}

function updateSpeakerVoiceVisibility() {
    const numSpeakers = parseInt(document.getElementById('{{ form.num_speakers.id }}').value);

    // Show/hide voice selection containers based on number of speakers
    document.getElementById('speaker1VoiceContainer').style.display = 'block';
    document.getElementById('speaker2VoiceContainer').style.display = numSpeakers >= 2 ? 'block' : 'none';
    document.getElementById('speaker3VoiceContainer').style.display = numSpeakers >= 3 ? 'block' : 'none';

    updateVoicePreview();
}

function updateVoicePreview() {
    const numSpeakers = parseInt(document.getElementById('{{ form.num_speakers.id }}').value);
    const speaker1Voice = document.getElementById('speaker1Voice');
    const speaker2Voice = document.getElementById('speaker2Voice');
    const speaker3Voice = document.getElementById('speaker3Voice');
    const previewDiv = document.getElementById('voicePreview');

    let previewHTML = '<div class="row">';

    // Speaker 1
    if (speaker1Voice.selectedOptions.length > 0) {
        previewHTML += `
            <div class="col-md-4 mb-2">
                <div class="d-flex align-items-center">
                    <i class="fas fa-user text-primary me-2"></i>
                    <div>
                        <strong>Speaker 1:</strong><br>
                        <small>${speaker1Voice.selectedOptions[0].text}</small>
                    </div>
                </div>
            </div>
        `;
    }

    // Speaker 2
    if (numSpeakers >= 2 && speaker2Voice.selectedOptions.length > 0) {
        previewHTML += `
            <div class="col-md-4 mb-2">
                <div class="d-flex align-items-center">
                    <i class="fas fa-user-friends text-success me-2"></i>
                    <div>
                        <strong>Speaker 2:</strong><br>
                        <small>${speaker2Voice.selectedOptions[0].text}</small>
                    </div>
                </div>
            </div>
        `;
    }

    // Speaker 3
    if (numSpeakers >= 3 && speaker3Voice.selectedOptions.length > 0) {
        previewHTML += `
            <div class="col-md-4 mb-2">
                <div class="d-flex align-items-center">
                    <i class="fas fa-users text-info me-2"></i>
                    <div>
                        <strong>Speaker 3:</strong><br>
                        <small>${speaker3Voice.selectedOptions[0].text}</small>
                    </div>
                </div>
            </div>
        `;
    }

    previewHTML += '</div>';
    previewDiv.innerHTML = previewHTML;
}

// Event listeners for voice and speaker changes
document.getElementById('{{ form.language.id }}').addEventListener('change', updateVoiceSelections);
document.getElementById('{{ form.num_speakers.id }}').addEventListener('change', updateSpeakerVoiceVisibility);
document.getElementById('speaker1Voice').addEventListener('change', updateVoicePreview);
document.getElementById('speaker2Voice').addEventListener('change', updateVoicePreview);
document.getElementById('speaker3Voice').addEventListener('change', updateVoicePreview);

// Subtitle/Caption functions
function toggleSubtitleOptions() {
    const subtitleCheckbox = document.getElementById('{{ form.include_subtitles.id }}');
    const subtitleOptions = document.getElementById('subtitle_options');

    if (subtitleCheckbox && subtitleOptions) {
        if (subtitleCheckbox.checked) {
            subtitleOptions.style.display = 'block';
        } else {
            subtitleOptions.style.display = 'none';
        }
    }
}

function updateCaptionPreview() {
    const styleSelect = document.getElementById('subtitle_style');
    const preview = document.getElementById('caption_preview');

    if (!styleSelect || !preview) return;

    const style = styleSelect.value;
    const previewText = preview.querySelector('div');

    if (!previewText) return;

    // Reset background
    preview.style.background = '#000';

    // Reset styles
    previewText.style.color = '';
    previewText.style.textShadow = '';
    previewText.style.fontWeight = '';
    previewText.style.webkitTextStroke = '';

    // Apply selected style
    switch(style) {
        case 'white_black_outline':
            previewText.style.color = 'white';
            previewText.style.webkitTextStroke = '2px black';
            previewText.style.textShadow = '2px 2px 4px black';
            break;
        case 'black_white_outline':
            previewText.style.color = 'black';
            previewText.style.webkitTextStroke = '2px white';
            previewText.style.textShadow = '2px 2px 4px white';
            preview.style.background = '#666';
            break;
        case 'yellow_black_outline':
            previewText.style.color = 'yellow';
            previewText.style.webkitTextStroke = '2px black';
            previewText.style.textShadow = '2px 2px 4px black';
            break;
        case 'white_shadow':
            previewText.style.color = 'white';
            previewText.style.textShadow = '3px 3px 6px rgba(0,0,0,0.8)';
            break;
        case 'bold_white':
            previewText.style.color = 'white';
            previewText.style.fontWeight = 'bold';
            previewText.style.textShadow = '1px 1px 2px black';
            break;
    }
}

// Event listeners for subtitle options
document.addEventListener('DOMContentLoaded', function() {
    const subtitleCheckbox = document.getElementById('{{ form.include_subtitles.id }}');
    const styleSelect = document.getElementById('subtitle_style');

    if (subtitleCheckbox) {
        subtitleCheckbox.addEventListener('change', toggleSubtitleOptions);
    }

    if (styleSelect) {
        styleSelect.addEventListener('change', updateCaptionPreview);
    }
});
</script>
{% endblock %}
