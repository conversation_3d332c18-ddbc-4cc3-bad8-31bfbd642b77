{% extends "base.html" %}

{% block title %}Admin Settings - AI Podcast Generator{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-6 fw-bold">
                        <i class="fas fa-cogs text-primary"></i> System Settings
                    </h1>
                    <p class="text-muted">Configure AI providers, API keys, and system preferences</p>
                </div>
                <div>
                    <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-secondary btn-custom">
                        <i class="fas fa-arrow-left"></i> Back to Admin
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" id="settingsForm">
        {{ form.hidden_tag() }}

        <!-- LLM Settings -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card card-custom">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-brain"></i> Large Language Model (LLM) Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.llm_provider.id }}" class="form-label fw-bold">
                                    <i class="fas fa-robot"></i> LLM Provider
                                </label>
                                {{ form.llm_provider(class="form-select", onchange="toggleLLMSettings()") }}
                                <div class="form-text">Choose your preferred LLM provider for content generation</div>
                            </div>
                        </div>

                        <div id="openai-llm-settings">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.openai_api_key.id }}" class="form-label fw-bold">
                                        <i class="fas fa-key"></i> OpenAI API Key
                                    </label>
                                    <div class="input-group">
                                        {{ form.openai_api_key(class="form-control", type="password", placeholder="sk-...") }}
                                        <button type="button" class="btn btn-outline-secondary" onclick="testApiKey('openai', '{{ form.openai_api_key.id }}')">
                                            <i class="fas fa-check"></i> Test
                                        </button>
                                    </div>
                                    <div class="form-text">Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.openai_model.id }}" class="form-label fw-bold">
                                        <i class="fas fa-microchip"></i> OpenAI Model
                                    </label>
                                    {{ form.openai_model(class="form-select") }}
                                    <div class="form-text">Choose the OpenAI model for content generation</div>
                                </div>
                            </div>
                        </div>

                        <div id="anthropic-llm-settings" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.anthropic_api_key.id }}" class="form-label fw-bold">
                                        <i class="fas fa-key"></i> Anthropic API Key
                                    </label>
                                    <div class="input-group">
                                        {{ form.anthropic_api_key(class="form-control", type="password", placeholder="sk-ant-...") }}
                                        <button type="button" class="btn btn-outline-secondary" onclick="testApiKey('anthropic', '{{ form.anthropic_api_key.id }}')">
                                            <i class="fas fa-check"></i> Test
                                        </button>
                                    </div>
                                    <div class="form-text">Get your API key from <a href="https://console.anthropic.com/" target="_blank">Anthropic Console</a></div>
                                </div>
                            </div>
                        </div>

                        <div id="google-llm-settings" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.google_api_key.id }}" class="form-label fw-bold">
                                        <i class="fas fa-key"></i> Google API Key
                                    </label>
                                    <div class="input-group">
                                        {{ form.google_api_key(class="form-control", type="password", placeholder="AIza...") }}
                                        <button type="button" class="btn btn-outline-secondary" onclick="testApiKey('google', '{{ form.google_api_key.id }}')">
                                            <i class="fas fa-check"></i> Test
                                        </button>
                                    </div>
                                    <div class="form-text">Get your API key from <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- TTS Settings -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card card-custom">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-volume-up"></i> Text-to-Speech (TTS) Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.tts_provider.id }}" class="form-label fw-bold">
                                    <i class="fas fa-microphone"></i> TTS Provider
                                </label>
                                {{ form.tts_provider(class="form-select", onchange="toggleTTSSettings()") }}
                                <div class="form-text">Choose your preferred TTS provider for voice generation</div>
                            </div>
                        </div>

                        <div id="elevenlabs-tts-settings">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.elevenlabs_api_key.id }}" class="form-label fw-bold">
                                        <i class="fas fa-key"></i> ElevenLabs API Key
                                    </label>
                                    <div class="input-group">
                                        {{ form.elevenlabs_api_key(class="form-control", type="password", placeholder="sk_...") }}
                                        <button type="button" class="btn btn-outline-secondary" onclick="testApiKey('elevenlabs', '{{ form.elevenlabs_api_key.id }}')">
                                            <i class="fas fa-check"></i> Test
                                        </button>
                                    </div>
                                    <div class="form-text">Get your API key from <a href="https://elevenlabs.io/" target="_blank">ElevenLabs</a></div>
                                </div>
                            </div>
                        </div>

                        <div id="openai-tts-settings" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.openai_tts_model.id }}" class="form-label fw-bold">
                                        <i class="fas fa-microchip"></i> OpenAI TTS Model
                                    </label>
                                    {{ form.openai_tts_model(class="form-select") }}
                                    <div class="form-text">Choose the OpenAI TTS model quality</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video Settings -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card card-custom">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-video"></i> Video Provider Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.video_provider.id }}" class="form-label fw-bold">
                                    <i class="fas fa-film"></i> Video Provider
                                </label>
                                {{ form.video_provider(class="form-select", onchange="toggleVideoSettings()") }}
                                <div class="form-text">Choose your preferred stock video provider</div>
                            </div>
                        </div>

                        <div id="pexels-video-settings">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.pexels_api_key.id }}" class="form-label fw-bold">
                                        <i class="fas fa-key"></i> Pexels API Key
                                    </label>
                                    <div class="input-group">
                                        {{ form.pexels_api_key(class="form-control", type="password", placeholder="Your Pexels API key") }}
                                        <button type="button" class="btn btn-outline-secondary" onclick="testApiKey('pexels', '{{ form.pexels_api_key.id }}')">
                                            <i class="fas fa-check"></i> Test
                                        </button>
                                    </div>
                                    <div class="form-text">Get your API key from <a href="https://www.pexels.com/api/" target="_blank">Pexels API</a></div>
                                </div>
                            </div>
                        </div>

                        <div id="unsplash-video-settings" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.unsplash_api_key.id }}" class="form-label fw-bold">
                                        <i class="fas fa-key"></i> Unsplash API Key
                                    </label>
                                    <div class="input-group">
                                        {{ form.unsplash_api_key(class="form-control", type="password", placeholder="Your Unsplash access key") }}
                                        <button type="button" class="btn btn-outline-secondary" onclick="testApiKey('unsplash', '{{ form.unsplash_api_key.id }}')">
                                            <i class="fas fa-check"></i> Test
                                        </button>
                                    </div>
                                    <div class="form-text">Get your API key from <a href="https://unsplash.com/developers" target="_blank">Unsplash Developers</a></div>
                                </div>
                            </div>
                        </div>

                        <div id="pixabay-video-settings" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.pixabay_api_key.id }}" class="form-label fw-bold">
                                        <i class="fas fa-key"></i> Pixabay API Key
                                    </label>
                                    <div class="input-group">
                                        {{ form.pixabay_api_key(class="form-control", type="password", placeholder="Your Pixabay API key") }}
                                        <button type="button" class="btn btn-outline-secondary" onclick="testApiKey('pixabay', '{{ form.pixabay_api_key.id }}')">
                                            <i class="fas fa-check"></i> Test
                                        </button>
                                    </div>
                                    <div class="form-text">Get your API key from <a href="https://pixabay.com/api/docs/" target="_blank">Pixabay API</a></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Settings -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card card-custom">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-sliders-h"></i> System Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.max_script_length.id }}" class="form-label fw-bold">
                                    <i class="fas fa-ruler"></i> Max Script Length
                                </label>
                                {{ form.max_script_length(class="form-select") }}
                                <div class="form-text">Maximum allowed script length for users</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.default_language.id }}" class="form-label fw-bold">
                                    <i class="fas fa-globe"></i> Default Language
                                </label>
                                {{ form.default_language(class="form-select") }}
                                <div class="form-text">Default language for new projects</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.enable_background_music.id }}" class="form-label fw-bold">
                                    <i class="fas fa-music"></i> Background Music
                                </label>
                                {{ form.enable_background_music(class="form-select") }}
                                <div class="form-text">Enable background music feature</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex gap-3">
                    <button type="submit" class="btn btn-primary btn-lg btn-custom" id="saveBtn">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-lg btn-custom" onclick="resetForm()">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- API Test Results -->
    <div id="testResults" class="mt-4" style="display: none;">
        <div class="alert" id="testAlert">
            <span id="testMessage"></span>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleLLMSettings() {
    const provider = document.getElementById('{{ form.llm_provider.id }}').value;

    // Hide all LLM settings
    document.getElementById('openai-llm-settings').style.display = 'none';
    document.getElementById('anthropic-llm-settings').style.display = 'none';
    document.getElementById('google-llm-settings').style.display = 'none';

    // Show relevant settings
    if (provider === 'openai') {
        document.getElementById('openai-llm-settings').style.display = 'block';
    } else if (provider === 'anthropic') {
        document.getElementById('anthropic-llm-settings').style.display = 'block';
    } else if (provider === 'google') {
        document.getElementById('google-llm-settings').style.display = 'block';
    }
}

function toggleTTSSettings() {
    const provider = document.getElementById('{{ form.tts_provider.id }}').value;

    // Hide all TTS settings
    document.getElementById('elevenlabs-tts-settings').style.display = 'none';
    document.getElementById('openai-tts-settings').style.display = 'none';

    // Show relevant settings
    if (provider === 'elevenlabs') {
        document.getElementById('elevenlabs-tts-settings').style.display = 'block';
    } else if (provider === 'openai') {
        document.getElementById('openai-tts-settings').style.display = 'block';
    }
}

function toggleVideoSettings() {
    const provider = document.getElementById('{{ form.video_provider.id }}').value;

    // Hide all video settings
    document.getElementById('pexels-video-settings').style.display = 'none';
    document.getElementById('unsplash-video-settings').style.display = 'none';
    document.getElementById('pixabay-video-settings').style.display = 'none';

    // Show relevant settings
    if (provider === 'pexels') {
        document.getElementById('pexels-video-settings').style.display = 'block';
    } else if (provider === 'unsplash') {
        document.getElementById('unsplash-video-settings').style.display = 'block';
    } else if (provider === 'pixabay') {
        document.getElementById('pixabay-video-settings').style.display = 'block';
    }
}

function testApiKey(provider, fieldId) {
    const apiKey = document.getElementById(fieldId).value;

    if (!apiKey) {
        showTestResult('error', 'Please enter an API key first');
        return;
    }

    // Show loading
    showTestResult('info', 'Testing API key...');

    fetch('/admin/settings/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            provider: provider,
            api_key: apiKey
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showTestResult('success', data.message);
        } else {
            showTestResult('error', data.message);
        }
    })
    .catch(error => {
        showTestResult('error', 'Error testing API key: ' + error.message);
    });
}

function showTestResult(type, message) {
    const resultsDiv = document.getElementById('testResults');
    const alertDiv = document.getElementById('testAlert');
    const messageSpan = document.getElementById('testMessage');

    // Set alert class
    alertDiv.className = 'alert';
    if (type === 'success') {
        alertDiv.classList.add('alert-success');
    } else if (type === 'error') {
        alertDiv.classList.add('alert-danger');
    } else {
        alertDiv.classList.add('alert-info');
    }

    messageSpan.textContent = message;
    resultsDiv.style.display = 'block';

    // Auto-hide after 5 seconds for success/error
    if (type !== 'info') {
        setTimeout(() => {
            resultsDiv.style.display = 'none';
        }, 5000);
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset all settings to their current saved values?')) {
        location.reload();
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleLLMSettings();
    toggleTTSSettings();
    toggleVideoSettings();
});

// Form submission
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    const saveBtn = document.getElementById('saveBtn');
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    saveBtn.disabled = true;
});
</script>
{% endblock %}
