{% extends "base.html" %}

{% block title %}Dashboard - AI Podcast Generator{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-6 fw-bold">
                        <i class="fas fa-tachometer-alt text-primary"></i> Dashboard
                    </h1>
                    <p class="text-muted">Welcome back, {{ current_user.username }}!</p>
                </div>
                <a href="{{ url_for('create_project') }}" class="btn btn-primary btn-custom">
                    <i class="fas fa-plus"></i> New Project
                </a>
            </div>
        </div>
    </div>

    {% if projects %}
        <div class="row">
            <div class="col-12">
                <h3 class="mb-4">Your Projects</h3>
                <div class="row">
                    {% for project in projects %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card card-custom h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h5 class="card-title">{{ project.title }}</h5>
                                        {% if project.status == 'completed' %}
                                            <span class="badge bg-success status-badge">
                                                <i class="fas fa-check"></i> Completed
                                            </span>
                                        {% elif project.status == 'processing' or project.status == 'generating_video' %}
                                            <span class="badge bg-warning status-badge">
                                                <i class="fas fa-spinner fa-spin"></i> Processing
                                            </span>
                                        {% elif project.status == 'content_ready' %}
                                            <span class="badge bg-info status-badge">
                                                <i class="fas fa-video"></i> Ready for Video
                                            </span>
                                        {% elif project.status == 'error' %}
                                            <span class="badge bg-danger status-badge">
                                                <i class="fas fa-exclamation-triangle"></i> Error
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary status-badge">
                                                <i class="fas fa-clock"></i> Created
                                            </span>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <p class="text-muted mb-2">
                                            <i class="fas fa-cog"></i> <strong>Type:</strong>
                                            {% if project.content_type == 'generate' %}
                                                <span class="badge bg-primary">AI Generated</span>
                                            {% else %}
                                                <span class="badge bg-success">Custom Script</span>
                                            {% endif %}
                                        </p>
                                        {% if project.keyword %}
                                        <p class="text-muted mb-2">
                                            <i class="fas fa-tag"></i> <strong>Keyword:</strong> {{ project.keyword }}
                                        </p>
                                        {% endif %}
                                        <p class="text-muted mb-2">
                                            <i class="fas fa-globe"></i> <strong>Language:</strong>
                                            {% if project.language == 'en' %}English
                                            {% elif project.language == 'es' %}Spanish
                                            {% elif project.language == 'fr' %}French
                                            {% elif project.language == 'de' %}German
                                            {% elif project.language == 'it' %}Italian
                                            {% elif project.language == 'pt' %}Portuguese
                                            {% elif project.language == 'ja' %}Japanese
                                            {% elif project.language == 'ko' %}Korean
                                            {% elif project.language == 'zh' %}Chinese
                                            {% else %}{{ project.language }}
                                            {% endif %}
                                        </p>
                                        <p class="text-muted mb-2">
                                            <i class="fas fa-users"></i> <strong>Speakers:</strong> {{ project.num_speakers }}
                                        </p>
                                        <p class="text-muted mb-2">
                                            <i class="fas fa-palette"></i> <strong>Tone:</strong> {{ project.tone|title }}
                                        </p>
                                        {% if project.user_media_file %}
                                        <p class="text-muted mb-2">
                                            <i class="fas fa-{% if project.user_media_type == 'image' %}image{% else %}video{% endif %} text-info"></i>
                                            <strong>Custom Media:</strong>
                                            <span class="badge bg-info">{{ project.user_media_type|title }} Uploaded</span>
                                        </p>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i> Created: {{ project.created_at.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <a href="{{ url_for('project_detail', project_id=project.id) }}"
                                           class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        {% if project.status == 'completed' and project.final_video %}
                                            <a href="{{ url_for('download_video', project_id=project.id) }}"
                                               class="btn btn-success btn-sm flex-fill">
                                                <i class="fas fa-download"></i> Download
                                            </a>
                                        {% endif %}
                                        <button class="btn btn-danger btn-sm" onclick="deleteProject('{{ project.id }}', '{{ project.title }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% else %}
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-microphone-alt fa-5x text-muted opacity-50"></i>
                    </div>
                    <h3 class="text-muted mb-3">No Projects Yet</h3>
                    <p class="text-muted mb-4">
                        Ready to create your first AI-powered podcast? It only takes a few minutes!
                    </p>
                    <a href="{{ url_for('create_project') }}" class="btn btn-primary btn-lg btn-custom">
                        <i class="fas fa-plus"></i> Create Your First Project
                    </a>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Quick Stats -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="mb-4">Quick Stats</h3>
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card card-custom text-center p-3">
                        <i class="fas fa-project-diagram fa-2x text-primary mb-2"></i>
                        <h4 class="fw-bold">{{ projects|length }}</h4>
                        <p class="text-muted mb-0">Total Projects</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card card-custom text-center p-3">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h4 class="fw-bold">{{ projects|selectattr('status', 'equalto', 'completed')|list|length }}</h4>
                        <p class="text-muted mb-0">Completed</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card card-custom text-center p-3">
                        <i class="fas fa-spinner fa-2x text-warning mb-2"></i>
                        <h4 class="fw-bold">{{ projects|selectattr('status', 'in', ['processing', 'generating_video'])|list|length }}</h4>
                        <p class="text-muted mb-0">Processing</p>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card card-custom text-center p-3">
                        <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                        <h4 class="fw-bold">{{ (projects|first).created_at.strftime('%b %d') if projects else 'N/A' }}</h4>
                        <p class="text-muted mb-0">Latest Project</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteProject(projectId, projectTitle) {
    if (confirm(`Are you sure you want to delete "${projectTitle}"?\n\nThis action cannot be undone and will delete all associated files.`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/delete/${projectId}`;

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
