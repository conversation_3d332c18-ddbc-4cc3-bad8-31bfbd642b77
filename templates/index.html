{% extends "base.html" %}

{% block content %}
<div class="gradient-bg">
    <div class="container">
        <div class="row min-vh-100 align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold text-white mb-4">
                    Create Amazing Podcasts with AI
                </h1>
                <p class="lead text-white mb-4">
                    Transform any keyword into engaging podcast content with multiple speakers,
                    professional voiceovers, and stunning visuals. Powered by cutting-edge AI technology.
                </p>
                <div class="mb-4">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center text-white">
                                <i class="fas fa-robot fa-2x text-warning me-3"></i>
                                <div>
                                    <h5 class="mb-1">AI-Generated Scripts</h5>
                                    <small>Smart content creation</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center text-white">
                                <i class="fas fa-users fa-2x text-success me-3"></i>
                                <div>
                                    <h5 class="mb-1">Multi-Speaker Support</h5>
                                    <small>Natural conversations</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center text-white">
                                <i class="fas fa-video fa-2x text-info me-3"></i>
                                <div>
                                    <h5 class="mb-1">Video Integration</h5>
                                    <small>Professional visuals</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center text-white">
                                <i class="fas fa-globe fa-2x text-danger me-3"></i>
                                <div>
                                    <h5 class="mb-1">Multi-Language</h5>
                                    <small>Global reach</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex gap-3 flex-wrap">
                    <a href="{{ url_for('register') }}" class="btn btn-warning btn-lg btn-custom">
                        <i class="fas fa-rocket"></i> Get Started Free
                    </a>
                    <a href="{{ url_for('demo_login') }}" class="btn btn-success btn-lg btn-custom">
                        <i class="fas fa-play"></i> Try Demo
                    </a>
                    <a href="#demo" class="btn btn-outline-light btn-lg btn-custom">
                        <i class="fas fa-info"></i> Learn More
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <div class="card card-custom bg-white p-4">
                        <h3 class="text-center mb-4">
                            <i class="fas fa-magic text-primary"></i> Quick Preview
                        </h3>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Enter a keyword:</label>
                            <input type="text" class="form-control" placeholder="e.g., Artificial Intelligence" id="demo-keyword">
                        </div>
                        <div class="row mb-3">
                            <div class="col-6">
                                <label class="form-label fw-bold">Language:</label>
                                <select class="form-select" id="demo-language">
                                    <option value="en">English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                </select>
                            </div>
                            <div class="col-6">
                                <label class="form-label fw-bold">Speakers:</label>
                                <select class="form-select" id="demo-speakers">
                                    <option value="1">1 Speaker</option>
                                    <option value="2">2 Speakers</option>
                                    <option value="3">3 Speakers</option>
                                </select>
                            </div>
                        </div>
                        <button class="btn btn-primary btn-custom w-100" onclick="showDemo()">
                            <i class="fas fa-wand-magic-sparkles"></i> Generate Preview
                        </button>
                        <div id="demo-result" class="mt-3" style="display: none;">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle"></i> Sample Output:</h6>
                                <small>
                                    "Welcome to today's discussion about <span id="demo-topic"></span>.
                                    I'm excited to explore this fascinating topic with you..."
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<section id="demo" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-5 fw-bold mb-4">How It Works</h2>
                <p class="lead mb-5">Create professional podcasts in just 4 simple steps</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3 text-center mb-4">
                <div class="card card-custom h-100 p-4">
                    <div class="mb-3">
                        <i class="fas fa-keyboard fa-3x text-primary"></i>
                    </div>
                    <h5>1. Enter Keyword</h5>
                    <p class="text-muted">Simply type in any topic you want to create a podcast about</p>
                </div>
            </div>
            <div class="col-md-3 text-center mb-4">
                <div class="card card-custom h-100 p-4">
                    <div class="mb-3">
                        <i class="fas fa-cogs fa-3x text-success"></i>
                    </div>
                    <h5>2. AI Generation</h5>
                    <p class="text-muted">Our AI creates engaging scripts and natural-sounding audio</p>
                </div>
            </div>
            <div class="col-md-3 text-center mb-4">
                <div class="card card-custom h-100 p-4">
                    <div class="mb-3">
                        <i class="fas fa-video fa-3x text-info"></i>
                    </div>
                    <h5>3. Choose Video</h5>
                    <p class="text-muted">Select from curated stock videos or use our generated backgrounds</p>
                </div>
            </div>
            <div class="col-md-3 text-center mb-4">
                <div class="card card-custom h-100 p-4">
                    <div class="mb-3">
                        <i class="fas fa-download fa-3x text-warning"></i>
                    </div>
                    <h5>4. Download & Share</h5>
                    <p class="text-muted">Get your professional podcast video ready to share anywhere</p>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-6 fw-bold mb-4">
                    <i class="fas fa-key text-warning"></i> Demo Credentials
                </h2>
                <p class="lead mb-5">Try the platform instantly with our demo accounts</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card card-custom h-100">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-user-shield fa-3x text-danger"></i>
                        </div>
                        <h5 class="fw-bold text-danger">Admin Account</h5>
                        <div class="bg-light p-3 rounded mb-3">
                            <p class="mb-1"><strong>Username:</strong> <code>admin</code></p>
                            <p class="mb-1"><strong>Password:</strong> <code>admin123</code></p>
                            <p class="mb-0 text-muted">Full system access & user management</p>
                        </div>
                        <a href="{{ url_for('login') }}" class="btn btn-danger btn-custom">
                            <i class="fas fa-shield-alt"></i> Admin Login
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card card-custom h-100">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <i class="fas fa-user fa-3x text-success"></i>
                        </div>
                        <h5 class="fw-bold text-success">Demo User</h5>
                        <div class="bg-light p-3 rounded mb-3">
                            <p class="mb-1"><strong>Username:</strong> <code>demo_user</code></p>
                            <p class="mb-1"><strong>Password:</strong> <code>demo123</code></p>
                            <p class="mb-0 text-muted">Standard user features & project creation</p>
                        </div>
                        <a href="{{ url_for('demo_login') }}" class="btn btn-success btn-custom">
                            <i class="fas fa-play"></i> Quick Demo
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 text-center">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> Demo accounts showcase the full interface and workflow.
                    Add your API keys to enable AI-powered content generation!
                </div>
            </div>
        </div>
    </div>
</section>

<section class="py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="display-6 fw-bold mb-4">Ready to Create Your First Podcast?</h2>
                <p class="lead mb-4">
                    Join thousands of content creators who are already using AI to produce
                    amazing podcast content in minutes, not hours.
                </p>
                <div class="d-flex gap-3 flex-wrap">
                    <a href="{{ url_for('register') }}" class="btn btn-primary btn-lg btn-custom">
                        <i class="fas fa-user-plus"></i> Sign Up Now
                    </a>
                    <a href="{{ url_for('demo_login') }}" class="btn btn-success btn-lg btn-custom">
                        <i class="fas fa-play"></i> Try Demo
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-podcast fa-10x text-primary opacity-25"></i>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block scripts %}
<script>
function showDemo() {
    const keyword = document.getElementById('demo-keyword').value || 'your topic';
    document.getElementById('demo-topic').textContent = keyword;
    document.getElementById('demo-result').style.display = 'block';
}
</script>
{% endblock %}
