#!/usr/bin/env python3
"""
Setup script to configure OpenAI API key
"""

import os
import sys

def setup_openai_key():
    """Setup OpenAI API key in the database"""
    print("🔧 Setting up OpenAI API Key...")
    print("=" * 50)
    
    # Check if user has an OpenAI API key
    print("📋 To use the AI Podcast Generator, you need an OpenAI API key.")
    print("🌐 Get your API key from: https://platform.openai.com/account/api-keys")
    print()
    
    # Get API key from user
    api_key = input("🔑 Please enter your OpenAI API key (starts with 'sk-'): ").strip()
    
    if not api_key:
        print("❌ No API key provided. Exiting...")
        return False
    
    if not api_key.startswith('sk-'):
        print("⚠️  Warning: OpenAI API keys usually start with 'sk-'")
        confirm = input("Continue anyway? (y/N): ").strip().lower()
        if confirm != 'y':
            return False
    
    try:
        # Update .env file
        env_content = []
        env_file = '.env'
        
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                env_content = f.readlines()
        
        # Update or add OpenAI API key
        updated = False
        for i, line in enumerate(env_content):
            if line.startswith('OPENAI_API_KEY='):
                env_content[i] = f'OPENAI_API_KEY={api_key}\n'
                updated = True
                break
        
        if not updated:
            env_content.append(f'OPENAI_API_KEY={api_key}\n')
        
        # Write back to file
        with open(env_file, 'w') as f:
            f.writelines(env_content)
        
        print("✅ OpenAI API key updated in .env file")
        
        # Also update in database
        from app import app, db, SystemSettings
        
        with app.app_context():
            # Check if setting already exists
            existing_setting = SystemSettings.query.filter_by(key='openai_api_key').first()
            
            if existing_setting:
                existing_setting.value = api_key
                print("✅ Updated existing OpenAI API key in database")
            else:
                # Create new setting
                new_setting = SystemSettings(
                    key='openai_api_key',
                    value=api_key,
                    description='OpenAI API Key for text generation and TTS',
                    category='ai',
                    is_secret=True
                )
                db.session.add(new_setting)
                print("✅ Added new OpenAI API key to database")
            
            db.session.commit()
            print("🎉 OpenAI API configuration completed!")
            
            return True
            
    except Exception as e:
        print(f"❌ Error setting up OpenAI API key: {e}")
        return False

def test_openai_api():
    """Test the OpenAI API key"""
    print("\n🧪 Testing OpenAI API...")
    
    try:
        from ai_services import AIService
        
        ai_service = AIService()
        
        # Test with a simple prompt
        test_prompt = "Generate a short 2-sentence podcast script about technology."
        
        print("🔍 Testing script generation...")
        script = ai_service.generate_script("technology", "en", 2, "conversational")
        
        if script and len(script) > 10:
            print("✅ OpenAI API working!")
            print(f"📝 Sample output: {script[:100]}...")
            return True
        else:
            print("❌ OpenAI API test failed - no content generated")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI API test failed: {e}")
        return False

def show_next_steps():
    """Show what to do next"""
    print("\n" + "="*60)
    print("🚀 NEXT STEPS")
    print("="*60)
    
    steps = [
        "1. ✅ OpenAI API key is configured",
        "2. 🔄 Restart the server: Ctrl+C then 'python run.py'",
        "3. 🌐 Visit: http://localhost:5000",
        "4. 🎯 Create a new project to test AI generation",
        "5. 🎬 Your Pexels API is already working for videos!",
        "",
        "💡 Features now available:",
        "   - AI script generation (OpenAI GPT)",
        "   - Multi-voice audio (OpenAI TTS)",
        "   - French language support",
        "   - Real Pexels videos",
        "   - Auto video creation",
        "   - Delete projects functionality"
    ]
    
    for step in steps:
        print(step)

def check_other_apis():
    """Check status of other APIs"""
    print("\n📊 API Status Check:")
    
    try:
        from app import app, SystemSettings
        
        with app.app_context():
            # Check Pexels API
            pexels_setting = SystemSettings.query.filter_by(key='pexels_api_key').first()
            if pexels_setting and pexels_setting.value:
                print(f"✅ Pexels API: Configured ({pexels_setting.value[:10]}...)")
            else:
                print("❌ Pexels API: Not configured")
            
            # Check OpenAI API
            openai_setting = SystemSettings.query.filter_by(key='openai_api_key').first()
            if openai_setting and openai_setting.value and not openai_setting.value.startswith('your_'):
                print(f"✅ OpenAI API: Configured ({openai_setting.value[:10]}...)")
            else:
                print("❌ OpenAI API: Not properly configured")
                
    except Exception as e:
        print(f"⚠️  Could not check API status: {e}")

if __name__ == "__main__":
    print("🔑 OpenAI API Key Setup")
    print("=" * 50)
    
    # Check current status
    check_other_apis()
    
    # Setup API key
    setup_success = setup_openai_key()
    
    if setup_success:
        # Test API
        test_success = test_openai_api()
        
        if test_success:
            print("\n🎉 SETUP COMPLETE!")
            show_next_steps()
        else:
            print("\n⚠️  Setup completed but API test failed")
            print("💡 The API key was saved. Try restarting the server.")
    else:
        print("\n❌ Setup failed")
        print("💡 You can manually edit the .env file to add your OpenAI API key")
