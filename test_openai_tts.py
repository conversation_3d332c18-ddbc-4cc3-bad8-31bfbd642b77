#!/usr/bin/env python3
"""
Test OpenAI Text-to-Speech API integration
"""

import openai
import os
from pydub import AudioSegment
import io

def test_openai_tts():
    """Test OpenAI TTS API with the provided key"""
    
    # API key from your example
    api_key = "************************************************************************************************************************************"
    
    print("🎙️ Testing OpenAI TTS API...")
    print(f"API Key: {api_key[:20]}...")
    
    try:
        # Create OpenAI client
        client = openai.OpenAI(api_key=api_key)
        
        # Test single voice
        print("\n🎵 Testing single voice generation...")
        response = client.audio.speech.create(
            model="tts-1",
            voice="alloy",
            input="Hello! This is a test of OpenAI Text-to-Speech API. Speaker 1 speaking with Alloy voice."
        )
        
        # Save test audio file
        output_file = "test_openai_single.mp3"
        with open(output_file, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ SUCCESS! Single voice audio saved to: {output_file}")
        print(f"File size: {os.path.getsize(output_file)} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

def test_multi_speaker_openai():
    """Test multi-speaker conversation with OpenAI TTS"""
    
    api_key = "************************************************************************************************************************************"
    
    print("\n🎭 Testing Multi-Speaker Conversation with OpenAI TTS...")
    
    try:
        client = openai.OpenAI(api_key=api_key)
        
        # Test script with different speakers
        speakers = [
            {
                "text": "Welcome to our podcast! I'm your host speaking with the Alloy voice.",
                "voice": "alloy",  # Female-ish
                "name": "Host (Alloy)"
            },
            {
                "text": "Thanks for having me! I'm the guest speaking with the Echo voice.",
                "voice": "echo",  # Male-ish
                "name": "Guest (Echo)"
            },
            {
                "text": "Let's talk about artificial intelligence and its impact on society.",
                "voice": "alloy",  # Female-ish
                "name": "Host (Alloy)"
            },
            {
                "text": "AI is transforming every industry. The possibilities are truly endless!",
                "voice": "echo",  # Male-ish
                "name": "Guest (Echo)"
            }
        ]
        
        audio_segments = []
        
        for i, speaker in enumerate(speakers):
            print(f"  Generating: {speaker['name']} - {speaker['text'][:40]}...")
            
            response = client.audio.speech.create(
                model="tts-1",
                voice=speaker["voice"],
                input=speaker["text"]
            )
            
            # Convert to AudioSegment
            audio_data = response.content
            audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
            
            # Add segment with pause
            audio_segments.append(audio_segment)
            if i < len(speakers) - 1:  # Don't add pause after last segment
                audio_segments.append(AudioSegment.silent(duration=500))  # 500ms pause
            
            print(f"    ✅ Generated {len(audio_data)} bytes")
        
        # Combine all segments
        if audio_segments:
            combined = AudioSegment.silent(duration=0)
            for segment in audio_segments:
                combined += segment
            
            # Add final silence
            combined += AudioSegment.silent(duration=500)
            
            output_file = "test_openai_multi_speaker.mp3"
            combined.export(output_file, format="mp3")
            
            print(f"✅ SUCCESS! Multi-speaker audio saved to: {output_file}")
            print(f"Combined duration: {len(combined) / 1000.0:.2f} seconds")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

def test_all_openai_voices():
    """Test all available OpenAI voices"""
    
    api_key = "************************************************************************************************************************************"
    
    print("\n🎵 Testing All OpenAI Voices...")
    
    voices = [
        {"name": "alloy", "description": "Natural & Balanced (Female-ish)"},
        {"name": "echo", "description": "Clear & Articulate (Male-ish)"},
        {"name": "fable", "description": "Expressive British Accent"},
        {"name": "onyx", "description": "Deep & Authoritative (Male)"},
        {"name": "nova", "description": "Young & Energetic (Female)"},
        {"name": "shimmer", "description": "Soft & Gentle (Female)"}
    ]
    
    try:
        client = openai.OpenAI(api_key=api_key)
        audio_segments = []
        
        for voice in voices:
            text = f"Hello, I am the {voice['name']} voice. {voice['description']}."
            print(f"  Testing voice: {voice['name']} - {voice['description']}")
            
            response = client.audio.speech.create(
                model="tts-1",
                voice=voice["name"],
                input=text
            )
            
            # Convert to AudioSegment
            audio_data = response.content
            audio_segment = AudioSegment.from_mp3(io.BytesIO(audio_data))
            
            # Add segment with pause
            audio_segments.append(audio_segment)
            audio_segments.append(AudioSegment.silent(duration=1000))  # 1 second pause
            
            print(f"    ✅ Generated {len(audio_data)} bytes")
        
        # Combine all voice samples
        if audio_segments:
            combined = AudioSegment.silent(duration=0)
            for segment in audio_segments:
                combined += segment
            
            output_file = "test_openai_all_voices.mp3"
            combined.export(output_file, format="mp3")
            
            print(f"✅ SUCCESS! All voices demo saved to: {output_file}")
            print(f"Total duration: {len(combined) / 1000.0:.2f} seconds")
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False

if __name__ == "__main__":
    print("🎙️ OpenAI TTS API Test Suite")
    print("=" * 50)
    
    # Test 1: Basic TTS
    test1 = test_openai_tts()
    
    # Test 2: Multi-speaker
    test2 = test_multi_speaker_openai()
    
    # Test 3: All voices
    test3 = test_all_openai_voices()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Basic OpenAI TTS: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"  Multi-Speaker: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"  All Voices: {'✅ PASS' if test3 else '❌ FAIL'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 ALL TESTS PASSED! OpenAI TTS is working correctly!")
        print("\n🎯 Available OpenAI Voices:")
        print("  • Alloy - Natural & Balanced (Female-ish)")
        print("  • Echo - Clear & Articulate (Male-ish)")
        print("  • Fable - Expressive British Accent")
        print("  • Onyx - Deep & Authoritative (Male)")
        print("  • Nova - Young & Energetic (Female)")
        print("  • Shimmer - Soft & Gentle (Female)")
    else:
        print("\n⚠️  Some tests failed. Check API key and network connection.")
