#!/usr/bin/env python3
"""
Test Pexels API integration
"""

import os
import sys

def test_pexels_api_direct():
    """Test Pexels API directly using the VideoService"""
    print("🎬 Testing Pexels API Integration...")
    
    try:
        from app import app
        from ai_services import VideoService
        
        with app.app_context():
            video_service = VideoService()
            
            # Test search
            print("🔍 Searching for 'technology' videos...")
            videos = video_service.search_videos("technology", per_page=3)
            
            if videos:
                print(f"✅ Found {len(videos)} videos from Pexels!")
                print("\n🎬 Sample Results:")
                for i, video in enumerate(videos):
                    print(f"  {i+1}. Duration: {video['duration']}s")
                    print(f"     Size: {video['width']}x{video['height']}")
                    print(f"     Preview: {video['preview'][:50]}...")
                    print(f"     Download: {video['download_url'][:50]}...")
                    print()
                return True
            else:
                print("❌ No videos found")
                return False
                
    except Exception as e:
        print(f"❌ Error testing Pexels API: {e}")
        return False

def test_pexels_settings():
    """Test that Pexels API key is properly configured"""
    print("\n🔧 Testing Pexels Configuration...")
    
    try:
        from app import app, SystemSettings
        
        with app.app_context():
            # Check if Pexels API key is set
            pexels_setting = SystemSettings.query.filter_by(key='pexels_api_key').first()
            
            if pexels_setting and pexels_setting.value:
                print(f"✅ Pexels API key configured: {pexels_setting.value[:10]}...")
                
                # Check video provider setting
                provider_setting = SystemSettings.query.filter_by(key='video_provider').first()
                if provider_setting and provider_setting.value == 'pexels':
                    print("✅ Video provider set to Pexels")
                    return True
                else:
                    print("⚠️  Video provider not set to Pexels")
                    return False
            else:
                print("❌ Pexels API key not configured")
                return False
                
    except Exception as e:
        print(f"❌ Error checking Pexels settings: {e}")
        return False

def test_video_search_different_keywords():
    """Test video search with different keywords"""
    print("\n🎯 Testing Different Search Keywords...")
    
    keywords = ["business", "nature", "abstract", "podcast"]
    
    try:
        from app import app
        from ai_services import VideoService
        
        with app.app_context():
            video_service = VideoService()
            
            for keyword in keywords:
                print(f"🔍 Searching for '{keyword}'...")
                videos = video_service.search_videos(keyword, per_page=2)
                
                if videos:
                    print(f"  ✅ Found {len(videos)} videos")
                    for video in videos:
                        print(f"    - {video['width']}x{video['height']}, {video['duration']}s")
                else:
                    print(f"  ❌ No videos found for '{keyword}'")
                
            return True
            
    except Exception as e:
        print(f"❌ Error testing keywords: {e}")
        return False

def show_integration_summary():
    """Show summary of Pexels integration features"""
    print("\n" + "="*60)
    print("🎬 PEXELS API INTEGRATION SUMMARY")
    print("="*60)
    
    features = [
        "✅ Pexels API Key: 563492ad6f9170000100000113003a14fe4f413ca1e1d8156475d931",
        "✅ Video Search: Search videos by keyword",
        "✅ High Quality: HD videos with multiple resolutions",
        "✅ Real Previews: Actual video thumbnails from Pexels",
        "✅ Direct Download: Download videos for processing",
        "✅ Auto Integration: Videos appear in project video options",
        "✅ Fallback Support: Generated backgrounds if Pexels fails",
        "",
        "🎯 How it works:",
        "  1. User creates project with keyword (e.g., 'technology')",
        "  2. System searches Pexels for related videos",
        "  3. Real video options appear alongside generated backgrounds",
        "  4. User selects preferred video",
        "  5. System downloads and combines with audio",
        "  6. Final podcast video ready for download",
        "",
        "🌐 Available in project detail page:",
        "  - Generated backgrounds (Abstract, Gradient, Minimal)",
        "  - Real Pexels videos based on project keyword",
        "  - Preview thumbnails for all options",
        "  - One-click video generation"
    ]
    
    for feature in features:
        print(feature)

def test_video_download_simulation():
    """Simulate video download process"""
    print("\n📥 Testing Video Download Simulation...")
    
    try:
        from app import app
        from ai_services import VideoService
        import requests
        
        with app.app_context():
            video_service = VideoService()
            
            # Get a video
            videos = video_service.search_videos("technology", per_page=1)
            
            if videos:
                video = videos[0]
                download_url = video['download_url']
                
                print(f"🎬 Testing download from: {download_url[:50]}...")
                
                # Test if URL is accessible (HEAD request)
                response = requests.head(download_url, timeout=10)
                
                if response.status_code == 200:
                    content_length = response.headers.get('content-length')
                    content_type = response.headers.get('content-type')
                    
                    print(f"✅ Video accessible")
                    print(f"   Size: {content_length} bytes")
                    print(f"   Type: {content_type}")
                    return True
                else:
                    print(f"❌ Video not accessible: {response.status_code}")
                    return False
            else:
                print("❌ No videos to test download")
                return False
                
    except Exception as e:
        print(f"❌ Error testing download: {e}")
        return False

if __name__ == "__main__":
    print("🎬 PEXELS API INTEGRATION TEST")
    print("="*50)
    
    # Run all tests
    test1 = test_pexels_settings()
    test2 = test_pexels_api_direct()
    test3 = test_video_search_different_keywords()
    test4 = test_video_download_simulation()
    
    # Calculate results
    tests = [test1, test2, test3, test4]
    passed = sum(tests)
    total = len(tests)
    
    print(f"\n📊 TEST RESULTS: {passed}/{total} PASSED")
    
    if passed >= 3:  # Allow one test to fail
        print("🎉 PEXELS INTEGRATION WORKING!")
        show_integration_summary()
    else:
        print("⚠️  Pexels integration needs attention")
        
    print(f"\n🌐 Visit: http://localhost:5000 to see Pexels videos in action!")
    print("📋 Manual Test Steps:")
    print("  1. Login to the application")
    print("  2. Create a new project with keyword 'technology'")
    print("  3. Wait for audio generation")
    print("  4. Click 'Create Video' button")
    print("  5. See real Pexels videos alongside generated backgrounds")
    print("  6. Select a Pexels video and generate final video")
