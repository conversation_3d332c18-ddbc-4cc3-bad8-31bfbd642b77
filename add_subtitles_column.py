#!/usr/bin/env python3
"""
Database migration to add include_subtitles column to the project table
"""

import sqlite3
import os
import sys

def add_subtitles_column():
    """Add include_subtitles column to existing database"""
    try:
        # Database path
        db_path = 'instance/podcast_generator.db'

        if not os.path.exists(db_path):
            print("❌ Database file not found!")
            return False

        print("🗄️  Adding include_subtitles column to project table...")

        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check if column already exists
        cursor.execute("PRAGMA table_info(project)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'include_subtitles' in columns:
            print("✅ include_subtitles column already exists")
            conn.close()
            return True

        # Add the column
        cursor.execute("""
            ALTER TABLE project
            ADD COLUMN include_subtitles BOOLEAN DEFAULT 1
        """)

        # Update existing projects to have subtitles enabled by default
        cursor.execute("""
            UPDATE project
            SET include_subtitles = 1
            WHERE include_subtitles IS NULL
        """)

        # Commit changes
        conn.commit()
        conn.close()

        print("✅ Successfully added include_subtitles column")
        print("📝 All existing projects now have subtitles enabled by default")

        return True

    except Exception as e:
        print(f"❌ Error adding column: {e}")
        return False

def verify_column():
    """Verify the column was added successfully"""
    try:
        db_path = 'instance/podcast_generator.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check table structure
        cursor.execute("PRAGMA table_info(project)")
        columns = cursor.fetchall()

        print("\n📋 Current project table structure:")
        for column in columns:
            col_id, name, col_type, not_null, default, pk = column
            print(f"  {name}: {col_type} (default: {default})")

        # Check if include_subtitles exists
        column_names = [col[1] for col in columns]
        if 'include_subtitles' in column_names:
            print("\n✅ include_subtitles column found!")

            # Count projects with subtitles enabled
            cursor.execute("SELECT COUNT(*) FROM project WHERE include_subtitles = 1")
            count = cursor.fetchone()[0]
            print(f"📊 {count} projects have subtitles enabled")

            conn.close()
            return True
        else:
            print("\n❌ include_subtitles column not found!")
            conn.close()
            return False

    except Exception as e:
        print(f"❌ Error verifying column: {e}")
        return False

if __name__ == "__main__":
    print("🗄️  Database Migration: Adding Subtitle Support")
    print("=" * 50)

    # Add the column
    success = add_subtitles_column()

    if success:
        # Verify it was added
        verify_success = verify_column()

        if verify_success:
            print("\n🎉 Migration completed successfully!")
            print("🎬 Subtitle feature is now ready to use!")
            print("🌐 Restart the server and try creating a project with subtitles!")
        else:
            print("\n⚠️  Migration completed but verification failed")
    else:
        print("\n❌ Migration failed")
        sys.exit(1)
