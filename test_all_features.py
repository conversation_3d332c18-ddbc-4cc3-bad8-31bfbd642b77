#!/usr/bin/env python3
"""
Test all new features:
1. Delete project functionality
2. Video options (fixed preview images)
3. French language support for OpenAI TTS
4. Auto video creation option
"""

import os
import sys
import requests
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_french_voices():
    """Test French language voices API"""
    print("🇫🇷 Testing French Voices...")
    
    url = "http://localhost:5000/api/voices"
    params = {"language": "fr"}
    
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            voices = data.get('voices', [])
            print(f"✅ Found {len(voices)} French voices:")
            for voice in voices:
                print(f"  - {voice['label']}")
            return True
        else:
            print(f"❌ Failed to get French voices: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing French voices: {e}")
        return False

def test_english_voices():
    """Test English language voices API"""
    print("\n🇺🇸 Testing English Voices...")
    
    url = "http://localhost:5000/api/voices"
    params = {"language": "en"}
    
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            voices = data.get('voices', [])
            print(f"✅ Found {len(voices)} English voices:")
            for voice in voices:
                print(f"  - {voice['label']}")
            return True
        else:
            print(f"❌ Failed to get English voices: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing English voices: {e}")
        return False

def test_project_status():
    """Test project status and check for auto_create_video field"""
    print("\n📊 Testing Project Status...")
    
    try:
        from app import app, db, Project
        
        with app.app_context():
            project = Project.query.filter_by(id="7435ce68-6577-476e-8303-1687d7b1c7f8").first()
            
            if project:
                print(f"✅ Project found: {project.title}")
                print(f"📊 Status: {project.status}")
                print(f"🎵 Audio file: {project.audio_file}")
                print(f"🎬 Final video: {project.final_video}")
                
                # Check if auto_create_video field exists
                if hasattr(project, 'auto_create_video'):
                    print(f"🤖 Auto create video: {project.auto_create_video}")
                    print("✅ Auto video creation field is available")
                else:
                    print("❌ Auto video creation field is missing")
                    return False
                
                return True
            else:
                print("❌ Project not found")
                return False
                
    except Exception as e:
        print(f"❌ Error checking project: {e}")
        return False

def test_video_options():
    """Test that video options show proper previews"""
    print("\n🎬 Testing Video Options...")
    
    # Test the video options by checking if they load properly
    url = "http://localhost:5000/project/7435ce68-6577-476e-8303-1687d7b1c7f8"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            content = response.text
            
            # Check for video option elements
            if "Abstract" in content and "Gradient" in content and "Minimal" in content:
                print("✅ Video options are present in the page")
                
                # Check for proper placeholder URLs (not showing "death")
                if "via.placeholder.com" in content:
                    print("✅ Placeholder images are being used")
                    
                    # Check that the URLs don't contain problematic text
                    if "death" not in content.lower():
                        print("✅ No problematic text in video options")
                        return True
                    else:
                        print("❌ Found problematic text in video options")
                        return False
                else:
                    print("⚠️  No placeholder images found")
                    return True  # Still OK if using other images
            else:
                print("❌ Video options not found in page")
                return False
        else:
            print(f"❌ Failed to load project page: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing video options: {e}")
        return False

def test_delete_functionality():
    """Test that delete functionality is available (without actually deleting)"""
    print("\n🗑️ Testing Delete Functionality...")
    
    try:
        # Check dashboard for delete buttons
        url = "http://localhost:5000/dashboard"
        response = requests.get(url)
        
        if response.status_code == 200:
            content = response.text
            
            if "deleteProject" in content and "fa-trash" in content:
                print("✅ Delete buttons found in dashboard")
                
                # Check project detail page
                url = "http://localhost:5000/project/7435ce68-6577-476e-8303-1687d7b1c7f8"
                response = requests.get(url)
                
                if response.status_code == 200:
                    content = response.text
                    
                    if "Delete Project" in content:
                        print("✅ Delete button found in project detail page")
                        return True
                    else:
                        print("❌ Delete button not found in project detail page")
                        return False
                else:
                    print(f"❌ Failed to load project detail page: {response.status_code}")
                    return False
            else:
                print("❌ Delete buttons not found in dashboard")
                return False
        else:
            print(f"❌ Failed to load dashboard: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing delete functionality: {e}")
        return False

def test_auto_video_form():
    """Test that the auto video creation option appears in the create form"""
    print("\n🤖 Testing Auto Video Creation Form...")
    
    try:
        url = "http://localhost:5000/create"
        response = requests.get(url)
        
        if response.status_code == 200:
            content = response.text
            
            if "auto_create_video" in content and "Automatically create video" in content:
                print("✅ Auto video creation option found in create form")
                
                if "Video Options" in content and "fa-magic" in content:
                    print("✅ Video options section properly styled")
                    return True
                else:
                    print("⚠️  Video options section styling could be improved")
                    return True
            else:
                print("❌ Auto video creation option not found in create form")
                return False
        else:
            print(f"❌ Failed to load create form: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing auto video form: {e}")
        return False

def check_database_migration():
    """Check if database has been updated with new fields"""
    print("\n💾 Checking Database Migration...")
    
    try:
        from app import app, db, Project
        
        with app.app_context():
            # Try to query the auto_create_video field
            projects = Project.query.all()
            
            for project in projects:
                if hasattr(project, 'auto_create_video'):
                    print(f"✅ Project {project.id[:8]}... has auto_create_video field")
                else:
                    print(f"❌ Project {project.id[:8]}... missing auto_create_video field")
                    return False
            
            print("✅ All projects have the new auto_create_video field")
            return True
            
    except Exception as e:
        print(f"❌ Database migration check failed: {e}")
        print("💡 You may need to recreate the database or run migrations")
        return False

if __name__ == "__main__":
    print("🧪 Testing All New Features")
    print("=" * 50)
    
    # Test 1: French language support
    test1 = test_french_voices()
    
    # Test 2: English language support (baseline)
    test2 = test_english_voices()
    
    # Test 3: Project status and auto video field
    test3 = test_project_status()
    
    # Test 4: Video options display
    test4 = test_video_options()
    
    # Test 5: Delete functionality
    test5 = test_delete_functionality()
    
    # Test 6: Auto video creation form
    test6 = test_auto_video_form()
    
    # Test 7: Database migration
    test7 = check_database_migration()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  French Voices: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"  English Voices: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"  Project Status: {'✅ PASS' if test3 else '❌ FAIL'}")
    print(f"  Video Options: {'✅ PASS' if test4 else '❌ FAIL'}")
    print(f"  Delete Function: {'✅ PASS' if test5 else '❌ FAIL'}")
    print(f"  Auto Video Form: {'✅ PASS' if test6 else '❌ FAIL'}")
    print(f"  Database Migration: {'✅ PASS' if test7 else '❌ FAIL'}")
    
    passed_tests = sum([test1, test2, test3, test4, test5, test6, test7])
    total_tests = 7
    
    print(f"\n📈 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL FEATURES WORKING PERFECTLY!")
        print("\n✨ New Features Summary:")
        print("  1. ✅ Delete projects with confirmation")
        print("  2. ✅ Fixed video preview images")
        print("  3. ✅ French language support for TTS")
        print("  4. ✅ Auto video creation option")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} features need attention")
        
    print("\n🚀 Visit: http://localhost:5000 to test the features!")
