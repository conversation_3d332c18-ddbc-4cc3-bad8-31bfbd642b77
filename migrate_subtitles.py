#!/usr/bin/env python3
"""
Database migration to add subtitle support to existing projects
"""

import os
import sys

def migrate_database():
    """Add include_subtitles column to existing projects"""
    try:
        from app import app, db
        
        with app.app_context():
            # Check if column already exists
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('project')]
            
            if 'include_subtitles' not in columns:
                print("🔄 Adding include_subtitles column to Project table...")
                
                # Add the column with default value True
                db.engine.execute(
                    'ALTER TABLE project ADD COLUMN include_subtitles BOOLEAN DEFAULT 1'
                )
                
                print("✅ Successfully added include_subtitles column")
                print("📝 All existing projects will have subtitles enabled by default")
            else:
                print("✅ include_subtitles column already exists")
                
            # Update any NULL values to True (default)
            db.engine.execute(
                'UPDATE project SET include_subtitles = 1 WHERE include_subtitles IS NULL'
            )
            
            print("🎉 Database migration completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

if __name__ == "__main__":
    print("🗄️  Database Migration: Adding Subtitle Support")
    print("=" * 50)
    
    success = migrate_database()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("🎬 Subtitle feature is now available for all projects!")
    else:
        print("\n❌ Migration failed. Please check the error above.")
        sys.exit(1)
