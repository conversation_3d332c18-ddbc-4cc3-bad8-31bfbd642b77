from celery import Celery
from ai_services import ContentGenerator, VoiceGenerator, VideoService
from video_processor import VideoProcessor, AudioProcessor
import os
import uuid
from flask import current_app
from app import db, Project, socketio
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Celery
celery = Celery('podcast_generator')
celery.config_from_object('celeryconfig')

@celery.task(bind=True)
def generate_podcast_content(self, project_id, keyword, language, num_speakers, tone):
    """Background task to generate podcast content"""
    try:
        # Update project status
        project = Project.query.get(project_id)
        if not project:
            return {'status': 'error', 'message': 'Project not found'}

        project.status = 'processing'
        db.session.commit()

        # Emit status update
        socketio.emit('status_update', {
            'project_id': project_id,
            'status': 'processing',
            'message': 'Generating content...'
        })

        # Initialize AI services
        content_generator = ContentGenerator()
        voice_generator = VoiceGenerator()

        # Generate script
        self.update_state(state='PROGRESS', meta={'current': 1, 'total': 4, 'status': 'Generating script...'})
        script = content_generator.generate_podcast_script(keyword, language, num_speakers, tone)

        if script.startswith('Error'):
            project.status = 'error'
            db.session.commit()
            return {'status': 'error', 'message': script}

        project.script = script
        db.session.commit()

        # Generate postcard text
        self.update_state(state='PROGRESS', meta={'current': 2, 'total': 4, 'status': 'Generating postcard...'})
        postcard_text = content_generator.generate_postcard_text(keyword, language, tone)
        project.postcard_text = postcard_text
        db.session.commit()

        # Generate audio
        self.update_state(state='PROGRESS', meta={'current': 3, 'total': 4, 'status': 'Generating audio...'})
        audio_filename = f"{project_id}_audio.mp3"
        audio_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'audio', audio_filename)

        if voice_generator.generate_audio_from_script(script, audio_path):
            project.audio_file = audio_filename
            db.session.commit()
        else:
            project.status = 'error'
            db.session.commit()
            return {'status': 'error', 'message': 'Failed to generate audio'}

        # Search for videos
        self.update_state(state='PROGRESS', meta={'current': 4, 'total': 4, 'status': 'Searching videos...'})
        video_service = VideoService()
        videos = video_service.search_videos(keyword)

        project.status = 'content_ready'
        db.session.commit()

        # Emit completion
        socketio.emit('content_generated', {
            'project_id': project_id,
            'script': script,
            'postcard_text': postcard_text,
            'videos': videos
        })

        return {
            'status': 'success',
            'script': script,
            'postcard_text': postcard_text,
            'videos': videos
        }

    except Exception as e:
        logger.error(f"Error in generate_podcast_content: {e}")
        project = Project.query.get(project_id)
        if project:
            project.status = 'error'
            db.session.commit()

        socketio.emit('status_update', {
            'project_id': project_id,
            'status': 'error',
            'message': str(e)
        })

        return {'status': 'error', 'message': str(e)}

@celery.task(bind=True)
def process_custom_content(self, project_id, custom_script, language, num_speakers, tone):
    """Background task to process user-provided content"""
    try:
        # Update project status
        project = Project.query.get(project_id)
        if not project:
            return {'status': 'error', 'message': 'Project not found'}

        project.status = 'processing'
        db.session.commit()

        # Emit status update
        socketio.emit('status_update', {
            'project_id': project_id,
            'status': 'processing',
            'message': 'Processing your content...'
        })

        # Initialize AI services
        content_generator = ContentGenerator()
        voice_generator = VoiceGenerator()

        # Use the custom script directly
        self.update_state(state='PROGRESS', meta={'current': 1, 'total': 4, 'status': 'Processing script...'})
        project.script = custom_script.strip()
        db.session.commit()

        # Generate postcard text based on the custom script
        self.update_state(state='PROGRESS', meta={'current': 2, 'total': 4, 'status': 'Generating postcard...'})

        # Extract key themes from the script for postcard generation
        script_preview = custom_script[:200] + "..." if len(custom_script) > 200 else custom_script
        postcard_prompt = f"Based on this podcast content: '{script_preview}', create a short postcard message"

        try:
            postcard_text = content_generator.generate_postcard_from_content(script_preview, language, tone)
        except:
            # Fallback postcard text
            postcard_text = f"Check out this amazing podcast content! A {tone} discussion that you won't want to miss."

        project.postcard_text = postcard_text
        db.session.commit()

        # Generate audio
        self.update_state(state='PROGRESS', meta={'current': 3, 'total': 4, 'status': 'Generating audio...'})
        audio_filename = f"{project_id}_audio.mp3"
        audio_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'audio', audio_filename)

        # Get voice selections (if any) - for future enhancement
        voice_selections = {}  # Could be passed from form data

        if voice_generator.generate_audio_from_script(custom_script, audio_path, language, voice_selections):
            project.audio_file = audio_filename
            db.session.commit()
        else:
            project.status = 'error'
            db.session.commit()
            return {'status': 'error', 'message': 'Failed to generate audio'}

        # Search for videos (use first few words as keyword)
        self.update_state(state='PROGRESS', meta={'current': 4, 'total': 4, 'status': 'Searching videos...'})
        video_service = VideoService()

        # Extract keywords from the script for video search
        script_words = custom_script.split()[:10]  # First 10 words
        search_keyword = ' '.join(script_words)
        videos = video_service.search_videos(search_keyword)

        project.status = 'content_ready'
        db.session.commit()

        # Emit completion
        socketio.emit('content_generated', {
            'project_id': project_id,
            'script': custom_script,
            'postcard_text': postcard_text,
            'videos': videos
        })

        return {
            'status': 'success',
            'script': custom_script,
            'postcard_text': postcard_text,
            'videos': videos
        }

    except Exception as e:
        logger.error(f"Error in process_custom_content: {e}")
        project = Project.query.get(project_id)
        if project:
            project.status = 'error'
            db.session.commit()

        socketio.emit('status_update', {
            'project_id': project_id,
            'status': 'error',
            'message': str(e)
        })

        return {'status': 'error', 'message': str(e)}

@celery.task(bind=True)
def generate_final_video(self, project_id, selected_video_url, background_music=None):
    """Background task to generate final video"""
    try:
        project = Project.query.get(project_id)
        if not project:
            return {'status': 'error', 'message': 'Project not found'}

        project.status = 'generating_video'
        db.session.commit()

        # Emit status update
        socketio.emit('status_update', {
            'project_id': project_id,
            'status': 'generating_video',
            'message': 'Creating final video...'
        })

        # Initialize processors
        video_processor = VideoProcessor()
        audio_processor = AudioProcessor()
        video_service = VideoService()

        # Determine video source: user upload, selected video, or fallback
        video_path = None

        if project.user_media_file and project.user_media_type:
            # Use user uploaded media
            if project.user_media_type == 'video':
                self.update_state(state='PROGRESS', meta={'current': 1, 'total': 5, 'status': 'Using your uploaded video...'})
                video_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'user_videos', project.user_media_file)
            elif project.user_media_type == 'image':
                self.update_state(state='PROGRESS', meta={'current': 1, 'total': 5, 'status': 'Creating video from your image...'})
                # Create video from static image
                audio_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'audio', project.audio_file)
                image_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'images', project.user_media_file)
                video_filename = f"{project_id}_from_image.mp4"
                video_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'video', video_filename)

                # Get audio duration for video length
                try:
                    import subprocess
                    import json
                    cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', audio_path]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        info = json.loads(result.stdout)
                        duration = float(info['format']['duration'])
                    else:
                        duration = 30.0
                except Exception:
                    duration = 30.0

                if not video_processor.create_video_from_image(image_path, video_path, duration):
                    video_path = None

        # If no user media or user media processing failed, download selected video
        if not video_path:
            self.update_state(state='PROGRESS', meta={'current': 1, 'total': 5, 'status': 'Downloading video...'})
            video_filename = f"{project_id}_video.mp4"
            video_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'video', video_filename)

            if not video_service.download_video(selected_video_url, video_path):
                # Create a simple background video if download fails
                audio_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'audio', project.audio_file)

            # Get audio duration using FFprobe instead of MoviePy
            try:
                import subprocess
                import json
                cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', audio_path]
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    info = json.loads(result.stdout)
                    duration = float(info['format']['duration'])
                else:
                    duration = 30.0  # Default fallback duration
            except Exception:
                duration = 30.0  # Default fallback duration

            if not video_processor.create_simple_background_video(duration, video_path):
                project.status = 'error'
                db.session.commit()
                return {'status': 'error', 'message': 'Failed to create video'}

        # Process audio
        self.update_state(state='PROGRESS', meta={'current': 2, 'total': 5, 'status': 'Processing audio...'})
        audio_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'audio', project.audio_file)
        processed_audio_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'audio', f"{project_id}_processed.mp3")

        # Normalize audio
        if not audio_processor.normalize_audio(audio_path, processed_audio_path):
            processed_audio_path = audio_path  # Use original if processing fails

        # Add fade effects
        faded_audio_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'audio', f"{project_id}_faded.mp3")
        if audio_processor.add_fade_effects(processed_audio_path, faded_audio_path):
            processed_audio_path = faded_audio_path

        # Resize video if needed
        self.update_state(state='PROGRESS', meta={'current': 3, 'total': 5, 'status': 'Processing video...'})
        resized_video_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'video', f"{project_id}_resized.mp4")
        if not video_processor.resize_video(video_path, resized_video_path):
            resized_video_path = video_path  # Use original if resizing fails

        # Combine audio and video
        self.update_state(state='PROGRESS', meta={'current': 4, 'total': 5, 'status': 'Combining audio and video...'})
        final_filename = f"{project_id}_final.mp4"
        final_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 'final', final_filename)

        background_music_path = None
        if background_music:
            # In a real implementation, you'd have background music files
            # For now, we'll skip background music
            pass

        if video_processor.combine_audio_video(resized_video_path, processed_audio_path, final_path, background_music_path):
            project.final_video = final_filename
            project.status = 'completed'
            db.session.commit()

            # Clean up temporary files
            for temp_file in [processed_audio_path, faded_audio_path, resized_video_path]:
                if os.path.exists(temp_file) and temp_file != audio_path and temp_file != video_path:
                    try:
                        os.remove(temp_file)
                    except:
                        pass

            # Emit completion
            socketio.emit('video_completed', {
                'project_id': project_id,
                'video_url': f'/download/{project_id}'
            })

            return {'status': 'success', 'video_path': final_path}
        else:
            project.status = 'error'
            db.session.commit()
            return {'status': 'error', 'message': 'Failed to combine audio and video'}

    except Exception as e:
        logger.error(f"Error in generate_final_video: {e}")
        project = Project.query.get(project_id)
        if project:
            project.status = 'error'
            db.session.commit()

        socketio.emit('status_update', {
            'project_id': project_id,
            'status': 'error',
            'message': str(e)
        })

        return {'status': 'error', 'message': str(e)}

@celery.task
def cleanup_old_files():
    """Periodic task to clean up old files"""
    try:
        import time
        from datetime import datetime, timedelta

        # Clean up files older than 7 days
        cutoff_time = time.time() - (7 * 24 * 60 * 60)

        upload_folder = current_app.config['UPLOAD_FOLDER']
        for subfolder in ['audio', 'video', 'final']:
            folder_path = os.path.join(upload_folder, subfolder)
            if os.path.exists(folder_path):
                for filename in os.listdir(folder_path):
                    file_path = os.path.join(folder_path, filename)
                    if os.path.isfile(file_path):
                        file_time = os.path.getmtime(file_path)
                        if file_time < cutoff_time:
                            try:
                                os.remove(file_path)
                                logger.info(f"Cleaned up old file: {file_path}")
                            except Exception as e:
                                logger.error(f"Error cleaning up file {file_path}: {e}")

        return {'status': 'success', 'message': 'Cleanup completed'}

    except Exception as e:
        logger.error(f"Error in cleanup_old_files: {e}")
        return {'status': 'error', 'message': str(e)}
