# 📸 User Media Upload Feature - Complete Guide

## 🎯 New Feature: Upload Your Own Images & Videos

The AI Podcast Generator now allows users to **upload their own images or videos** instead of relying solely on stock footage. This gives users complete creative control over the visual content of their podcasts.

---

## 🚀 Key Features

### **📸 Image Upload Support**
- **Formats**: JPG, JPEG, PNG, GIF
- **Size Limit**: 10MB maximum
- **Processing**: Automatically converts to video with audio duration
- **Quality**: Maintains aspect ratio with professional scaling

### **🎬 Video Upload Support**
- **Formats**: MP4, MOV, AVI, WEBM
- **Size Limit**: 50MB maximum
- **Processing**: Uses your video as the visual background
- **Compatibility**: Works with all audio lengths

### **🎨 Smart Processing**
- **Auto-scaling**: Fits content to 1920x1080 (1080p)
- **Aspect Ratio**: Preserves original proportions with padding
- **Duration Matching**: Images extended to match audio length
- **Quality Optimization**: Professional video encoding

### **🔄 Flexible Workflow**
- **Optional Upload**: Stock footage used if no media uploaded
- **Priority System**: User media takes precedence over stock
- **Fallback Support**: Graceful degradation if processing fails
- **Real-time Preview**: See your media before processing

---

## 🎛️ How to Use

### **Step 1: Create New Project**
1. **Login**: Use demo_user/demo123 or your account
2. **Navigate**: Go to "Create New Project"
3. **Choose Mode**: Select AI generation or paste script

### **Step 2: Upload Your Media**
1. **Find Upload Section**: Look for "Upload Your Own Media (Optional)"
2. **Choose File**: Click "Choose File" or drag & drop
3. **Preview**: See instant preview of your media
4. **Verify**: Check file size and format

### **Step 3: Complete Project Setup**
1. **Configure Settings**: Set language, speakers, tone
2. **Add Content**: Enter keyword or paste script
3. **Review**: Check all settings including uploaded media
4. **Create**: Click "Generate Podcast" or "Create Podcast"

### **Step 4: Processing**
1. **Media Processing**: System uses your uploaded media
2. **Audio Generation**: Creates professional voice synthesis
3. **Video Creation**: Combines your media with generated audio
4. **Final Output**: Downloads as professional MP4

---

## 📋 Supported Formats & Limits

### **🖼️ Image Formats**
```
✅ JPG/JPEG - Standard photo format
✅ PNG - Transparent backgrounds supported
✅ GIF - Animated GIFs converted to video
📏 Max Size: 10MB
🎯 Recommended: 1920x1080 or higher resolution
```

### **🎬 Video Formats**
```
✅ MP4 - Most compatible format
✅ MOV - Apple/QuickTime format
✅ AVI - Windows video format
✅ WEBM - Web-optimized format
📏 Max Size: 50MB
🎯 Recommended: 1920x1080, 30fps
```

### **⚡ Processing Specs**
```
📺 Output Resolution: 1920x1080 (1080p)
🎞️ Frame Rate: 30fps
🎵 Audio Quality: High-fidelity synthesis
📦 Final Format: MP4 (H.264)
```

---

## 🎨 Creative Use Cases

### **📚 Educational Content**
- **Lecture Slides**: Upload presentation slides as images
- **Diagrams**: Use educational diagrams and charts
- **Screenshots**: Convert screen captures to video podcasts
- **Infographics**: Transform static infographics into audio-visual content

### **🏢 Business Presentations**
- **Company Logos**: Brand your podcasts with company visuals
- **Product Images**: Showcase products while discussing them
- **Team Photos**: Add personal touch to corporate content
- **Office Videos**: Use workplace footage as background

### **🎭 Creative Projects**
- **Artwork**: Display your art while discussing creative process
- **Photography**: Share photo stories with narration
- **Travel Photos**: Create travel podcasts with your images
- **Personal Videos**: Use home videos for family podcasts

### **📰 News & Journalism**
- **Event Photos**: Use news photos for current events
- **Location Videos**: Show places being discussed
- **Interview Footage**: Combine with AI-generated discussion
- **Documentary Style**: Create documentary-style podcasts

---

## 🔧 Technical Implementation

### **Upload Processing**
```python
# File validation and storage
if form.user_media.data:
    file = form.user_media.data
    file_ext = file.filename.rsplit('.', 1)[1].lower()
    
    # Determine media type
    if file_ext in ['jpg', 'jpeg', 'png', 'gif']:
        user_media_type = 'image'
        upload_folder = 'uploads/images'
    elif file_ext in ['mp4', 'mov', 'avi', 'webm']:
        user_media_type = 'video'
        upload_folder = 'uploads/user_videos'
```

### **Video Generation Priority**
```python
# Processing priority system
if project.user_media_file:
    # 1. Use user uploaded media (highest priority)
    video_source = user_uploaded_path
elif video_url:
    # 2. Use selected stock video
    video_source = stock_video_url
else:
    # 3. Generate background video (fallback)
    video_source = generated_background
```

### **Image to Video Conversion**
```bash
# FFmpeg command for image to video
ffmpeg -y -loop 1 -i image.jpg \
  -c:v libx264 -t duration \
  -pix_fmt yuv420p \
  -vf "scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2" \
  -r 30 output.mp4
```

---

## 🎯 User Interface Features

### **📤 Drag & Drop Upload**
- **Easy Upload**: Drag files directly onto upload area
- **Visual Feedback**: Highlights drop zone when dragging
- **Instant Processing**: Immediate file validation
- **Error Handling**: Clear error messages for invalid files

### **👁️ Real-time Preview**
- **Image Preview**: Thumbnail with file info
- **Video Preview**: Playable video with controls
- **File Details**: Shows filename and size
- **Remove Option**: Easy removal before processing

### **📊 Progress Indicators**
- **Upload Status**: Shows file upload progress
- **Processing Steps**: Real-time processing updates
- **Media Integration**: Confirms media usage
- **Completion Notice**: Success confirmation

### **🔍 Quality Assurance**
- **Format Validation**: Checks file types
- **Size Limits**: Enforces reasonable file sizes
- **Preview Quality**: Shows how media will appear
- **Error Recovery**: Graceful handling of issues

---

## 🎪 Demo Examples

### **Quick Test - Image Upload**
1. **Login**: http://localhost:5000/demo_login
2. **Create Project**: Choose "Paste Your Own Text"
3. **Upload Image**: Add any JPG/PNG image
4. **Add Script**: 
   ```
   Speaker 1: Welcome to our visual podcast!
   Speaker 2: Today we're showcasing user-uploaded content.
   Speaker 1: This feature gives creators complete control.
   ```
5. **Process**: Watch your image become a professional podcast!

### **Quick Test - Video Upload**
1. **Same Steps**: Follow image example above
2. **Upload Video**: Use MP4/MOV video instead
3. **Result**: Your video becomes the podcast background
4. **Download**: Get professional MP4 with your content

---

## 🔄 Workflow Comparison

### **Stock Footage Workflow**
```
1. Enter keyword
2. AI finds stock videos
3. User selects from options
4. System downloads and processes
5. Creates final video
```

### **User Upload Workflow**
```
1. Upload your media
2. Preview and confirm
3. System processes immediately
4. Creates final video
⚡ Faster and more personal!
```

---

## 🎯 Benefits

### **🎨 Creative Control**
- **Personal Branding**: Use your own visuals
- **Unique Content**: Stand out from stock footage
- **Brand Consistency**: Match your visual identity
- **Artistic Expression**: Showcase your creativity

### **⚡ Efficiency**
- **No Search Time**: Skip stock footage browsing
- **Instant Processing**: Use your content immediately
- **Perfect Match**: Content exactly matches your vision
- **Faster Workflow**: Streamlined creation process

### **💼 Professional Quality**
- **High Resolution**: Maintains image/video quality
- **Proper Scaling**: Professional aspect ratio handling
- **Smooth Integration**: Seamless audio-video sync
- **Broadcast Ready**: Professional MP4 output

---

## 🚀 Getting Started

### **Try It Now**
1. **Demo Access**: http://localhost:5000/demo_login
2. **Create Project**: Click "Create New Project"
3. **Upload Media**: Add your image or video
4. **Add Content**: Paste script or enter keyword
5. **Generate**: Create your personalized podcast!

### **Best Practices**
- **High Resolution**: Use 1920x1080 or higher for best quality
- **Appropriate Content**: Ensure media matches your audio
- **File Size**: Keep under limits for faster processing
- **Format Choice**: MP4 for video, JPG for images work best

---

**🎬 Your content, your vision, professional results! Upload and create today! ✨**
