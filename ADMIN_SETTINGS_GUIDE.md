# 🔧 Admin Settings Management - Complete Guide

## 🎯 New Feature: Comprehensive Admin Settings

The AI Podcast Generator now includes a **powerful admin settings panel** that allows administrators to configure all AI providers, API keys, and system preferences through a user-friendly web interface.

---

## 🚀 Key Features

### **🔑 Dynamic API Key Management**
- **Database Storage**: API keys stored securely in database
- **Environment Fallback**: Falls back to .env if database empty
- **Real-time Testing**: Test API keys before saving
- **Secure Display**: API keys masked in interface

### **🤖 Multiple LLM Providers**
- **OpenAI GPT**: GPT-3.5 Turbo, GPT-4, GPT-4 Turbo
- **Anthropic Claude**: Claude models (ready for integration)
- **Google Gemini**: Google AI models (ready for integration)
- **Local Models**: Support for local LLM deployment

### **🎙️ Multiple TTS Providers**
- **ElevenLabs**: High-quality voice synthesis (primary)
- **OpenAI TTS**: TTS-1 and TTS-1 HD models
- **Google Cloud TTS**: Enterprise-grade TTS (ready)
- **Azure Cognitive Services**: Microsoft TTS (ready)

### **🎬 Multiple Video Providers**
- **Pexels**: Free and premium stock videos (primary)
- **Unsplash**: High-quality stock footage (ready)
- **Pixabay**: Free stock videos (ready)
- **AI Generated**: Fallback to generated backgrounds

### **⚙️ System Configuration**
- **Script Length Limits**: 2K, 5K, 10K, or unlimited
- **Default Language**: Set system-wide default
- **Background Music**: Enable/disable feature
- **Provider Selection**: Choose primary providers

---

## 🔐 Access & Security

### **Admin Access**
- **Login Required**: Admin privileges needed
- **Secure Routes**: Protected endpoints
- **Role-based Access**: Only admins can modify settings

### **Default Admin Account**
```
Username: admin
Password: admin123
```

### **API Key Security**
- **Encrypted Storage**: Secure database storage
- **Masked Display**: Keys hidden in interface
- **Test Before Save**: Validate keys before storing
- **Environment Fallback**: Graceful degradation

---

## 🎛️ Settings Categories

### **🧠 LLM Settings**
```
Provider Selection:
├── OpenAI GPT (Primary)
│   ├── API Key: sk-...
│   └── Model: gpt-3.5-turbo, gpt-4, gpt-4-turbo
├── Anthropic Claude
│   └── API Key: sk-ant-...
├── Google Gemini
│   └── API Key: AIza...
└── Local Model
    └── Endpoint configuration
```

### **🎵 TTS Settings**
```
Provider Selection:
├── ElevenLabs (Primary)
│   └── API Key: sk_...
├── OpenAI TTS
│   └── Model: tts-1, tts-1-hd
├── Google Cloud TTS
│   └── Service Account Key
└── Azure Cognitive Services
    └── Subscription Key
```

### **🎬 Video Settings**
```
Provider Selection:
├── Pexels (Primary)
│   └── API Key: Your-Pexels-Key
├── Unsplash
│   └── Access Key: Your-Access-Key
├── Pixabay
│   └── API Key: Your-Pixabay-Key
└── AI Generated Only
    └── No external videos
```

### **⚙️ System Settings**
```
Configuration Options:
├── Max Script Length: 2K/5K/10K/Unlimited
├── Default Language: en/es/fr/de/it/pt/ja/ko/zh
└── Background Music: Enabled/Disabled
```

---

## 🔧 How to Use

### **Step 1: Access Admin Panel**
1. **Login as Admin**: Use admin/admin123
2. **Navigate to Admin**: Click "Admin Dashboard"
3. **Open Settings**: Click "System Settings" button

### **Step 2: Configure LLM Provider**
1. **Select Provider**: Choose from dropdown
2. **Enter API Key**: Paste your API key
3. **Test Connection**: Click "Test" button
4. **Choose Model**: Select specific model (if applicable)

### **Step 3: Configure TTS Provider**
1. **Select Provider**: Choose TTS service
2. **Enter API Key**: Add your credentials
3. **Test Connection**: Verify key works
4. **Set Model**: Choose quality level

### **Step 4: Configure Video Provider**
1. **Select Provider**: Choose stock video service
2. **Enter API Key**: Add your credentials
3. **Test Connection**: Verify access
4. **Set as Primary**: System will use this provider

### **Step 5: System Settings**
1. **Script Limits**: Set maximum length
2. **Default Language**: Choose system default
3. **Features**: Enable/disable background music

### **Step 6: Save Configuration**
1. **Review Settings**: Check all configurations
2. **Save Changes**: Click "Save Settings"
3. **Verify Success**: Check confirmation message

---

## 🧪 API Key Testing

### **Real-time Validation**
- **OpenAI**: Tests with minimal API call
- **ElevenLabs**: Validates with voice list request
- **Pexels**: Checks access with search request
- **Instant Feedback**: Success/error messages

### **Test Results**
```
✅ Success: "OpenAI API key is valid"
❌ Error: "Invalid ElevenLabs API key"
ℹ️ Info: "Testing API key..."
```

---

## 🔄 Dynamic Configuration

### **Runtime Updates**
- **No Restart Required**: Changes apply immediately
- **Fallback System**: Environment variables as backup
- **Graceful Degradation**: System continues if keys missing

### **Provider Switching**
```python
# System automatically uses configured providers
llm_provider = get_setting('llm_provider', 'openai')
tts_provider = get_setting('tts_provider', 'elevenlabs')
video_provider = get_setting('video_provider', 'pexels')
```

---

## 📊 Settings Database Schema

### **SystemSettings Table**
```sql
CREATE TABLE system_settings (
    id INTEGER PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    description VARCHAR(500),
    category VARCHAR(50),
    is_secret BOOLEAN DEFAULT FALSE,
    created_at DATETIME,
    updated_at DATETIME
);
```

### **Example Records**
```sql
INSERT INTO system_settings VALUES
('llm_provider', 'openai', 'Primary LLM provider', 'llm', FALSE),
('openai_api_key', 'sk-...', 'OpenAI API Key', 'llm', TRUE),
('tts_provider', 'elevenlabs', 'Primary TTS provider', 'tts', FALSE),
('elevenlabs_api_key', 'sk_...', 'ElevenLabs API Key', 'tts', TRUE);
```

---

## 🎯 Use Cases

### **🏢 Enterprise Deployment**
- **Multiple API Keys**: Different keys for different environments
- **Provider Redundancy**: Fallback providers for reliability
- **Cost Management**: Switch providers based on usage
- **Compliance**: Use specific providers for regulations

### **🧪 Development & Testing**
- **Easy Switching**: Test different providers quickly
- **Key Management**: Separate dev/prod keys
- **Feature Testing**: Enable/disable features easily
- **Performance Comparison**: Compare provider quality

### **🎓 Educational Use**
- **Limited Resources**: Set script length limits
- **Cost Control**: Use free tiers effectively
- **Language Support**: Configure for specific regions
- **Feature Control**: Enable only needed features

---

## 🔧 Technical Implementation

### **Dynamic Client Creation**
```python
def get_openai_client():
    api_key = get_api_key('openai')
    if api_key:
        return openai.OpenAI(api_key=api_key)
    return None

def get_elevenlabs_client():
    api_key = get_api_key('elevenlabs')
    if api_key:
        return ElevenLabs(api_key=api_key)
    return None
```

### **Settings Helper Functions**
```python
def get_setting(key, default=None):
    setting = SystemSettings.query.filter_by(key=key).first()
    return setting.value if setting else default

def set_setting(key, value, description=None, category=None, is_secret=False):
    # Update or create setting
    # Commit to database
```

---

## 🎉 Benefits

### **🔧 Administrative Control**
- **Centralized Management**: All settings in one place
- **Real-time Updates**: No server restarts needed
- **Easy Testing**: Validate configurations instantly
- **Secure Storage**: Database-backed key management

### **🚀 Operational Flexibility**
- **Provider Switching**: Change providers without code changes
- **Cost Optimization**: Use most cost-effective providers
- **Feature Control**: Enable/disable features per deployment
- **Scalability**: Easy to add new providers

### **👥 User Experience**
- **Consistent Service**: Automatic fallbacks ensure uptime
- **Quality Control**: Choose best providers for each service
- **Language Support**: Configure for target audience
- **Performance**: Optimize for speed vs. quality

---

## 🎯 Getting Started

### **Quick Setup**
1. **Login**: Use admin/admin123
2. **Navigate**: Admin Dashboard → System Settings
3. **Configure**: Add your API keys
4. **Test**: Verify each key works
5. **Save**: Apply configuration
6. **Use**: Create podcasts with new settings!

### **Demo Access**
- **Admin Panel**: http://localhost:5000/admin
- **Settings Page**: http://localhost:5000/admin/settings
- **Test API**: Built-in testing functionality

---

**🔧 Complete control over your AI Podcast Generator! Configure once, use everywhere! ✨**
