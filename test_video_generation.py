#!/usr/bin/env python3
"""
Test video generation functionality
"""

import os
import sys
import requests
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_video_generation():
    """Test the video generation API endpoint"""
    print("🎬 Testing Video Generation...")
    
    # Test data
    project_id = "7435ce68-6577-476e-8303-1687d7b1c7f8"
    video_url = "abstract_bg"
    
    # API endpoint
    url = "http://localhost:5000/api/generate_video"
    
    # Request data
    data = {
        "project_id": project_id,
        "video_url": video_url
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"📡 Sending request to: {url}")
        print(f"📦 Data: {data}")
        
        # Make the request
        response = requests.post(url, json=data, headers=headers, timeout=120)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Response: {result}")
            
            if result.get('status') == 'completed':
                print("🎉 Video generation completed successfully!")
                return True
            elif result.get('status') == 'error':
                print(f"❌ Video generation failed: {result.get('message', 'Unknown error')}")
                return False
            else:
                print(f"⚠️ Unexpected status: {result.get('status')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out (this is normal for video generation)")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_direct_video_generation():
    """Test video generation directly using the function"""
    print("\n🎬 Testing Direct Video Generation...")
    
    try:
        from app import app, generate_final_video_sync
        
        with app.app_context():
            project_id = "7435ce68-6577-476e-8303-1687d7b1c7f8"
            video_url = "gradient_bg"
            
            print(f"🎯 Testing with project: {project_id}")
            print(f"🎨 Background type: {video_url}")
            
            success = generate_final_video_sync(project_id, video_url)
            
            if success:
                print("✅ Direct video generation successful!")
                
                # Check if files were created
                video_file = f"uploads/video/{project_id}_generated_video.mp4"
                final_file = f"uploads/final/{project_id}_final.mp4"
                
                if os.path.exists(video_file):
                    size = os.path.getsize(video_file)
                    print(f"📹 Generated video: {video_file} ({size} bytes)")
                
                if os.path.exists(final_file):
                    size = os.path.getsize(final_file)
                    print(f"🎬 Final video: {final_file} ({size} bytes)")
                
                return True
            else:
                print("❌ Direct video generation failed!")
                return False
                
    except Exception as e:
        print(f"❌ Error in direct test: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking Dependencies...")
    
    dependencies = {
        'ffmpeg': 'ffmpeg -version',
        'PIL': 'python -c "from PIL import Image; print(Image.__version__)"',
        'requests': 'python -c "import requests; print(requests.__version__)"'
    }
    
    all_good = True
    
    for name, command in dependencies.items():
        try:
            import subprocess
            result = subprocess.run(command.split(), capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.strip().split('\n')[0]
                print(f"✅ {name}: {version}")
            else:
                print(f"❌ {name}: Not working")
                all_good = False
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
            all_good = False
    
    return all_good

def check_project_status():
    """Check the current project status"""
    print("\n📊 Checking Project Status...")
    
    try:
        from app import app, db, Project
        
        with app.app_context():
            project = Project.query.filter_by(id="7435ce68-6577-476e-8303-1687d7b1c7f8").first()
            
            if project:
                print(f"✅ Project found: {project.title}")
                print(f"📊 Status: {project.status}")
                print(f"🎵 Audio file: {project.audio_file}")
                print(f"🎬 Final video: {project.final_video}")
                
                # Check file existence
                if project.audio_file:
                    audio_path = f"uploads/audio/{project.audio_file}"
                    if os.path.exists(audio_path):
                        size = os.path.getsize(audio_path)
                        print(f"🎵 Audio exists: {size} bytes")
                    else:
                        print("❌ Audio file missing")
                
                return True
            else:
                print("❌ Project not found")
                return False
                
    except Exception as e:
        print(f"❌ Error checking project: {e}")
        return False

if __name__ == "__main__":
    print("🎬 Video Generation Test Suite")
    print("=" * 50)
    
    # Test 1: Check dependencies
    test1 = check_dependencies()
    
    # Test 2: Check project status
    test2 = check_project_status()
    
    # Test 3: Direct video generation
    test3 = test_direct_video_generation()
    
    # Test 4: API endpoint (optional, might require authentication)
    # test4 = test_video_generation()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Dependencies: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"  Project Status: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"  Direct Video Gen: {'✅ PASS' if test3 else '❌ FAIL'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 ALL TESTS PASSED! Video generation is working!")
        print("\n🎯 Generated Files:")
        for folder in ['uploads/video', 'uploads/final']:
            if os.path.exists(folder):
                files = os.listdir(folder)
                for file in files:
                    if file.endswith('.mp4'):
                        path = os.path.join(folder, file)
                        size = os.path.getsize(path)
                        print(f"  - {path} ({size} bytes)")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")
        
    print("\n🚀 The Video Generation is ready for use!")
    print("Visit: http://localhost:5000/project/7435ce68-6577-476e-8303-1687d7b1c7f8")
