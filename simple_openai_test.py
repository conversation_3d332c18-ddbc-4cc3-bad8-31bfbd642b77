#!/usr/bin/env python3
"""
Simple OpenAI TTS test without pydub
"""

import openai
import os

def test_openai_simple():
    """Test OpenAI TTS API"""
    
    api_key = "************************************************************************************************************************************"
    
    print("🎙️ Testing OpenAI TTS API...")
    
    try:
        client = openai.OpenAI(api_key=api_key)
        
        # Test different voices
        voices = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
        
        for voice in voices:
            print(f"Testing voice: {voice}")
            
            response = client.audio.speech.create(
                model="tts-1",
                voice=voice,
                input=f"Hello, I am the {voice} voice from OpenAI TTS."
            )
            
            output_file = f"test_{voice}.mp3"
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ Generated: {output_file} ({os.path.getsize(output_file)} bytes)")
        
        print("\n🎉 All OpenAI voices working!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_openai_simple()
