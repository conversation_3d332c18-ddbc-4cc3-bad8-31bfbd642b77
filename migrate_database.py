#!/usr/bin/env python3
"""
Migrate database to add new auto_create_video column
"""

import os
import sys
import sqlite3

def migrate_database():
    """Add auto_create_video column to existing database"""
    db_path = "instance/podcast_generator.db"

    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False

    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check if column already exists
        cursor.execute("PRAGMA table_info(project)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'auto_create_video' in columns:
            print("✅ auto_create_video column already exists")
            return True

        # Add the new column
        cursor.execute("ALTER TABLE project ADD COLUMN auto_create_video BOOLEAN DEFAULT 0")
        conn.commit()

        print("✅ Successfully added auto_create_video column")

        # Verify the column was added
        cursor.execute("PRAGMA table_info(project)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'auto_create_video' in columns:
            print("✅ Column verified in database")
            return True
        else:
            print("❌ Column not found after adding")
            return False

    except Exception as e:
        print(f"❌ Error migrating database: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🔄 Migrating Database...")
    success = migrate_database()

    if success:
        print("🎉 Database migration completed successfully!")
    else:
        print("❌ Database migration failed!")

    print("\n🔄 Restart the server to apply changes.")
