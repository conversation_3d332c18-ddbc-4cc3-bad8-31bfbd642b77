#!/usr/bin/env python3
"""
Test script to verify all the fixes are working:
1. "AI Podcast" text removed from generated videos
2. User media upload functionality
3. Pexels content filtering
"""

import os
import sys

def test_ai_podcast_text_removal():
    """Test that 'AI Podcast' text is no longer added to generated videos"""
    print("🎬 Testing AI Podcast Text Removal")
    print("=" * 50)
    
    try:
        # Check the app.py file for the removed text
        with open('app.py', 'r') as f:
            content = f.read()
            
        if 'text = "AI Podcast"' in content:
            print("❌ 'AI Podcast' text still found in app.py")
            return False
        else:
            print("✅ 'AI Podcast' text successfully removed from video generation")
            
        # Check for the comment indicating removal
        if '# Remove the "AI Podcast" text - user requested to remove it' in content:
            print("✅ Removal comment found - change was intentional")
        else:
            print("⚠️ No removal comment found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking AI Podcast text removal: {e}")
        return False

def test_user_media_upload():
    """Test user media upload functionality"""
    print("\n📸 Testing User Media Upload Functionality")
    print("=" * 50)
    
    try:
        from app import app, ProjectForm
        
        with app.app_context():
            # Test form has user_media field
            form = ProjectForm()
            
            if hasattr(form, 'user_media'):
                print("✅ user_media field exists in ProjectForm")
                
                # Check field configuration
                field = form.user_media
                print(f"✅ Field type: {type(field).__name__}")
                
                # Check validators
                validators = [v.__class__.__name__ for v in field.validators]
                print(f"✅ Validators: {validators}")
                
                if 'FileAllowed' in validators:
                    print("✅ FileAllowed validator found - file type restrictions in place")
                else:
                    print("⚠️ FileAllowed validator not found")
                    
            else:
                print("❌ user_media field missing from ProjectForm")
                return False
                
        # Check template has media upload section
        try:
            with open('templates/create_project.html', 'r') as f:
                template_content = f.read()
                
            if 'Upload Your Own Media' in template_content:
                print("✅ Media upload section found in template")
            else:
                print("❌ Media upload section missing from template")
                return False
                
            if 'previewMedia' in template_content:
                print("✅ Media preview functionality found in template")
            else:
                print("❌ Media preview functionality missing from template")
                return False
                
        except Exception as e:
            print(f"❌ Error checking template: {e}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing user media upload: {e}")
        return False

def test_pexels_content_filtering():
    """Test Pexels content filtering"""
    print("\n🔍 Testing Pexels Content Filtering")
    print("=" * 50)
    
    try:
        # Check ai_services.py for content filtering
        with open('ai_services.py', 'r') as f:
            content = f.read()
            
        if 'problematic_terms' in content:
            print("✅ Problematic terms filtering found")
        else:
            print("❌ Problematic terms filtering missing")
            return False
            
        if 'enhanced_search' in content:
            print("✅ Enhanced search with positive modifiers found")
        else:
            print("❌ Enhanced search missing")
            return False
            
        # Check for specific problematic terms
        problematic_terms = ['death', 'violence', 'war', 'accident', 'injury', 'blood']
        for term in problematic_terms:
            if f"'{term}'" in content:
                print(f"✅ Filtering for '{term}' found")
            else:
                print(f"⚠️ Filtering for '{term}' not found")
                
        return True
        
    except Exception as e:
        print(f"❌ Error testing Pexels content filtering: {e}")
        return False

def test_video_generation_with_user_media():
    """Test video generation logic with user media"""
    print("\n🎥 Testing Video Generation with User Media")
    print("=" * 50)
    
    try:
        # Check app.py for user media handling in video generation
        with open('app.py', 'r') as f:
            content = f.read()
            
        if 'project.user_media_file' in content:
            print("✅ User media file checking found in video generation")
        else:
            print("❌ User media file checking missing")
            return False
            
        if 'user_image_path' in content:
            print("✅ User image processing found")
        else:
            print("❌ User image processing missing")
            return False
            
        if 'user_video_path' in content:
            print("✅ User video processing found")
        else:
            print("❌ User video processing missing")
            return False
            
        if 'img.resize((width, height)' in content:
            print("✅ Image resizing for aspect ratio found")
        else:
            print("❌ Image resizing for aspect ratio missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing video generation with user media: {e}")
        return False

def show_summary():
    """Show summary of implemented fixes"""
    print("\n" + "="*70)
    print("🎉 SUMMARY OF IMPLEMENTED FIXES")
    print("="*70)
    
    fixes = [
        "✅ REMOVED 'AI Podcast' text from generated videos",
        "✅ ADDED user media upload functionality (images & videos)",
        "✅ ADDED media preview in create form",
        "✅ ADDED Pexels content filtering to avoid inappropriate content",
        "✅ ADDED user media processing in video generation",
        "✅ ADDED automatic image resizing for different aspect ratios",
        "✅ ADDED support for user videos in video generation",
        "✅ ENHANCED search terms with positive modifiers",
        "✅ MAINTAINED backward compatibility with existing projects"
    ]
    
    for fix in fixes:
        print(fix)
        
    print("\n🎯 USER REQUESTS ADDRESSED:")
    print("1. ❌ Removed 'AI Podcast' text from videos")
    print("2. 📸 Added user image/video upload capability")
    print("3. 🔒 Fixed inappropriate Pexels content with filtering")
    
    print("\n🌐 HOW TO USE:")
    print("1. Go to http://localhost:5000/create")
    print("2. Upload your own image or video in the 'Upload Your Own Media' section")
    print("3. Create your podcast normally")
    print("4. When generating video, your media will be used automatically")
    print("5. No more 'AI Podcast' text will appear on generated backgrounds")
    print("6. Pexels searches now filter out inappropriate content")

if __name__ == "__main__":
    print("🔧 Testing All Implemented Fixes")
    print("=" * 50)
    
    tests = [
        test_ai_podcast_text_removal,
        test_user_media_upload,
        test_pexels_content_filtering,
        test_video_generation_with_user_media
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
    
    # Show results
    print("\n" + "="*50)
    print("📊 TEST RESULTS")
    print("="*50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        show_summary()
    else:
        print(f"\n⚠️ {total - passed} tests failed")
        
    print(f"\n🌐 Test your fixes at: http://localhost:5000/create")
