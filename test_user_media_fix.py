#!/usr/bin/env python3
"""
Test script to verify user media upload functionality is working correctly
"""

import os
import sys

def test_user_media_logic():
    """Test the user media logic in video generation"""
    print("🎬 Testing User Media Upload Fix")
    print("=" * 50)
    
    try:
        # Check the API route logic
        with open('app.py', 'r') as f:
            content = f.read()
            
        # Check if user media prioritization is implemented
        if 'if project.user_media_file:' in content and 'video_url = \'user_media\'' in content:
            print("✅ User media prioritization found in API route")
        else:
            print("❌ User media prioritization missing from API route")
            return False
            
        # Check if auto-video creation uses user media
        if 'video_url = \'user_media\' if project.user_media_file else \'gradient_bg\'' in content:
            print("✅ Auto-video creation uses user media when available")
        else:
            print("❌ Auto-video creation doesn't prioritize user media")
            return False
            
        # Check if video generation logic handles user media
        if 'selected_video_url == \'user_media\'' in content:
            print("✅ Video generation logic handles user_media identifier")
        else:
            print("❌ Video generation logic missing user_media handling")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing user media logic: {e}")
        return False

def test_frontend_user_media():
    """Test frontend user media handling"""
    print("\n🌐 Testing Frontend User Media Handling")
    print("=" * 50)
    
    try:
        # Check the template logic
        with open('templates/project_detail.html', 'r') as f:
            content = f.read()
            
        # Check if user media detection is implemented
        if 'userMediaFile' in content and 'userMediaType' in content:
            print("✅ User media detection found in template")
        else:
            print("❌ User media detection missing from template")
            return False
            
        # Check if user media is shown as primary option
        if 'User Media Detected' in content:
            print("✅ User media shown as primary option")
        else:
            print("❌ User media not prioritized in UI")
            return False
            
        # Check if auto-selection is implemented
        if 'selectedVideoUrl = \'user_media\'' in content:
            print("✅ User media auto-selection implemented")
        else:
            print("❌ User media auto-selection missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing frontend user media: {e}")
        return False

def test_file_upload_handling():
    """Test file upload and storage logic"""
    print("\n📁 Testing File Upload Handling")
    print("=" * 50)
    
    try:
        with open('app.py', 'r') as f:
            content = f.read()
            
        # Check if file upload logic exists
        if 'form.user_media.data' in content:
            print("✅ File upload form handling found")
        else:
            print("❌ File upload form handling missing")
            return False
            
        # Check if file type detection exists
        if 'image_extensions' in content and 'video_extensions' in content:
            print("✅ File type detection implemented")
        else:
            print("❌ File type detection missing")
            return False
            
        # Check if unique filename generation exists
        if 'uuid.uuid4()' in content and 'unique_filename' in content:
            print("✅ Unique filename generation implemented")
        else:
            print("❌ Unique filename generation missing")
            return False
            
        # Check if upload directories are created
        if 'os.makedirs(upload_folder, exist_ok=True)' in content:
            print("✅ Upload directory creation implemented")
        else:
            print("❌ Upload directory creation missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing file upload handling: {e}")
        return False

def show_usage_instructions():
    """Show instructions for testing the fix"""
    print("\n" + "="*70)
    print("🧪 HOW TO TEST USER MEDIA UPLOAD FIX")
    print("="*70)
    
    print("\n🎯 STEP-BY-STEP TESTING:")
    print("1. 🌐 Go to: http://localhost:5000/create")
    print("2. 📝 Fill in project details (any topic)")
    print("3. 📸 Upload an image or video in 'Upload Your Own Media' section")
    print("4. 👀 See the preview appear")
    print("5. 🚀 Create the project")
    print("6. ⏳ Wait for audio generation to complete")
    print("7. 🎬 Click 'Create Video' button")
    print("8. 👁️ You should see:")
    print("   ✅ 'User Media Detected' alert")
    print("   ✅ Your uploaded media shown as primary option")
    print("   ✅ 'Generate Video with My Media' button")
    print("   ✅ Auto-selection of your media")
    print("9. 🎥 Click 'Generate Video with My Media'")
    print("10. 📥 Download and check the final video")
    
    print("\n🔍 WHAT TO EXPECT:")
    print("✅ Your uploaded image/video should be used as the background")
    print("✅ No need to select from Pexels videos")
    print("✅ Automatic prioritization of user media")
    print("✅ Clear indication that user media will be used")
    
    print("\n🚨 IF IT DOESN'T WORK:")
    print("1. Check server logs for errors")
    print("2. Verify file was uploaded successfully")
    print("3. Check upload folder permissions")
    print("4. Try with different file formats")
    
    print("\n📊 SERVER LOGS TO WATCH FOR:")
    print("✅ 'User has uploaded media: filename.ext (image/video)'")
    print("✅ 'Using user media instead of selected video'")
    print("✅ 'Using user-uploaded media: filename.ext (image/video)'")
    print("✅ 'Converting user image to video' OR 'Using user video'")
    print("✅ 'Created video from user image' OR 'Using user video'")

if __name__ == "__main__":
    print("🔧 Testing User Media Upload Fix")
    print("=" * 50)
    
    tests = [
        test_user_media_logic,
        test_frontend_user_media,
        test_file_upload_handling
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
    
    # Show results
    print("\n" + "="*50)
    print("📊 TEST RESULTS")
    print("="*50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ User media upload functionality should now work correctly!")
        print("✅ User media will be automatically prioritized in video generation")
        print("✅ Frontend will show user media as the primary option")
        print("✅ Auto-video creation will use user media when available")
    else:
        print(f"\n⚠️ {total - passed} tests failed")
        print("❌ User media upload may not work correctly")
        
    show_usage_instructions()
    
    print(f"\n🌐 Test your fix at: http://localhost:5000/create")
    print("📸 Upload an image or video and see if it's used in the final video!")
